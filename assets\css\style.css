.table td, .table th { vertical-align: middle !important; }
.badge { font-size: 0.8em; padding: 0.4em 0.6em; }
.small-box { color: #fff !important; }
.small-box .icon > i { font-size: 70px; top: 20px; }

/* 表头筛选样式 */
.filterable-header {
    position: relative;
    cursor: pointer;
    user-select: none;
}

.filterable-header:hover {
    background-color: rgba(0,0,0,0.05);
}

.filterable-header.filtered {
    background-color: rgba(0,123,255,0.1);
}

.filter-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    min-width: 200px;
}

.filter-dropdown .dropdown-menu {
    max-height: 300px;
    overflow-y: auto;
}

.filter-option {
    cursor: pointer;
}

.filter-option:hover {
    background-color: #f8f9fa;
}

/* 设备列表样式 */
.device-list {
    max-height: 120px;
    overflow-y: auto;
}

.device-item {
    padding: 2px 0;
    border-bottom: 1px solid #eee;
}

.device-item:last-child {
    border-bottom: none;
}