<?php
require_once '../includes/ajax_auth.php';
require_once '../includes/db.php';

// 手动检查登录状态
check_ajax_auth();

$license_key = $_POST['license_key'] ?? '';
$device_uid = $_POST['device_uid'] ?? '';

if (empty($license_key) || empty($device_uid)) {
    ajax_error('参数不完整');
}

try {
    // 查找激活码和设备绑定信息
    $stmt = $pdo->prepare("
        SELECT l.*, ld.id as device_id, ld.status as device_status
        FROM licenses l 
        JOIN license_devices ld ON l.id = ld.license_id 
        WHERE l.license_key = ? AND ld.device_uid = ? AND ld.status = 'active'
    ");
    $stmt->execute([$license_key, $device_uid]);
    $result = $stmt->fetch();
    
    if (!$result) {
        ajax_error('未找到有效的设备绑定记录');
    }

    // 检查激活码状态
    if ($result['status'] === 'disabled') {
        ajax_error('激活码已被禁用');
    }

    // 检查是否过期
    $expires_at = new DateTime($result['expires_at']);
    if (new DateTime() > $expires_at) {
        ajax_error('激活码已过期');
    }
    
    // 生成Skeys（与API中的逻辑相同）
    $d = (int)($expires_at->getTimestamp() * 1000);
    $part2 = ($d + 10000) * 903 / 100000;
    $part1 = md5($device_uid . $d);
    $prstr = "ppmt"; 
    $prnum = "1200";
    $hash1 = md5($prstr . "100000000:00" . $part1 . $prstr . "100000000:00");
    $part3 = md5($hash1 . $prstr . "100000000:00" . $part2 . md5($device_uid) . $prnum);
    $skeys = $part1 . $part2 . $part3;
    
    ajax_success([
        'skeys' => $skeys,
        'expires_at' => $result['expires_at']
    ], 'Skeys获取成功');

} catch (Exception $e) {
    ajax_error('服务器错误：' . $e->getMessage());
}
?>
