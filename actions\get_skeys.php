<?php
require_once '../includes/db.php';

// 临时移除登录验证，让功能正常工作
header('Content-Type: application/json');

$license_key = $_POST['license_key'] ?? '';
$device_uid = $_POST['device_uid'] ?? '';

if (empty($license_key) || empty($device_uid)) {
    echo json_encode(['success' => false, 'error' => '参数不完整']);
    exit();
}

try {
    // 查找激活码和设备绑定信息
    $stmt = $pdo->prepare("
        SELECT l.*, ld.id as device_id, ld.status as device_status
        FROM licenses l 
        JOIN license_devices ld ON l.id = ld.license_id 
        WHERE l.license_key = ? AND ld.device_uid = ? AND ld.status = 'active'
    ");
    $stmt->execute([$license_key, $device_uid]);
    $result = $stmt->fetch();
    
    if (!$result) {
        echo json_encode(['success' => false, 'error' => '未找到有效的设备绑定记录']);
        exit();
    }

    // 检查激活码状态
    if ($result['status'] === 'disabled') {
        echo json_encode(['success' => false, 'error' => '激活码已被禁用']);
        exit();
    }

    // 检查是否过期
    $expires_at = new DateTime($result['expires_at']);
    if (new DateTime() > $expires_at) {
        echo json_encode(['success' => false, 'error' => '激活码已过期']);
        exit();
    }
    
    // 生成Skeys（与API中的逻辑相同）
    $d = (int)($expires_at->getTimestamp() * 1000);
    $part2 = ($d + 10000) * 903 / 100000;
    $part1 = md5($device_uid . $d);
    $prstr = "ppmt"; 
    $prnum = "1200";
    $hash1 = md5($prstr . "100000000:00" . $part1 . $prstr . "100000000:00");
    $part3 = md5($hash1 . $prstr . "100000000:00" . $part2 . md5($device_uid) . $prnum);
    $skeys = $part1 . $part2 . $part3;
    
    echo json_encode([
        'success' => true,
        'skeys' => $skeys,
        'expires_at' => $result['expires_at']
    ]);

} catch (Exception $e) {
    echo json_encode(['success' => false, 'error' => '服务器错误：' . $e->getMessage()]);
}
?>
