<?php
require_once '../includes/check_login.php';
require_once '../includes/db.php';

// --- 处理 POST 请求 (修改备注、增加时间) ---
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $id = (int)($_POST['id'] ?? 0);

    if ($id <= 0) {
        header('Location: ../index.php'); exit();
    }

    // 修改备注
    if ($action === 'update_notes' && isset($_POST['notes'])) {
        $notes = $_POST['notes'];
        $stmt = $pdo->prepare("UPDATE licenses SET notes = ? WHERE id = ?");
        $stmt->execute([$notes, $id]);
    }
    
    // 增加时间
    if ($action === 'add_time') {
        $add_value = (int)($_POST['add_value'] ?? 0);
        $add_unit = $_POST['add_unit'] ?? '';

        if ($add_value > 0 && in_array($add_unit, ['hour', 'day', 'month', 'year'])) {
            $stmt = $pdo->prepare("SELECT expires_at, status FROM licenses WHERE id = ?");
            $stmt->execute([$id]);
            $license = $stmt->fetch();

            if ($license) {
                // 如果已过期，则从当前时间开始加；如果未过期，则从原到期时间开始加
                $base_time = (new DateTime() > new DateTime($license['expires_at'])) ? new DateTime() : new DateTime($license['expires_at']);
                $base_time->modify("+" . $add_value . " " . $add_unit);
                $new_expires_at = $base_time->format('Y-m-d H:i:s');

                // 如果是从过期状态续费，状态变回 active
                $new_status = ($license['status'] === 'expired') ? 'active' : $license['status'];

                $update_stmt = $pdo->prepare("UPDATE licenses SET expires_at = ?, status = ? WHERE id = ?");
                $update_stmt->execute([$new_expires_at, $new_status, $id]);
            }
        }
    }
}

// --- 处理 GET 请求 (解绑) ---
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    $action = $_GET['action'] ?? '';
    $id = (int)($_GET['id'] ?? 0);

    if ($id <= 0) {
        header('Location: ../index.php'); exit();
    }

    // 解绑设备
    if ($action === 'unbind') {
        $stmt = $pdo->prepare("UPDATE licenses SET device_uid = NULL, status = 'inactive', activated_at = NULL, last_ip = NULL, unbind_count = unbind_count + 1 WHERE id = ?");
        $stmt->execute([$id]);
    }
}

header('Location: ../index.php');
exit();