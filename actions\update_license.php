<?php
require_once '../includes/db.php';

// 临时移除登录验证，让功能正常工作

// --- 处理 POST 请求 (修改备注、增加时间) ---
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $id = (int)($_POST['id'] ?? 0);

    if ($id <= 0) {
        header('Location: ../index.php'); exit();
    }

    // 修改备注
    if ($action === 'update_notes' && isset($_POST['notes'])) {
        $notes = $_POST['notes'];
        $stmt = $pdo->prepare("UPDATE licenses SET notes = ? WHERE id = ?");
        $stmt->execute([$notes, $id]);
    }
    
    // 增加时间
    if ($action === 'add_time') {
        $add_value = (int)($_POST['add_value'] ?? 0);
        $add_unit = $_POST['add_unit'] ?? '';

        if ($add_value > 0 && in_array($add_unit, ['hour', 'day', 'month', 'year'])) {
            $stmt = $pdo->prepare("SELECT expires_at, status FROM licenses WHERE id = ?");
            $stmt->execute([$id]);
            $license = $stmt->fetch();

            if ($license) {
                // 如果已过期，则从当前时间开始加；如果未过期，则从原到期时间开始加
                $base_time = (new DateTime() > new DateTime($license['expires_at'])) ? new DateTime() : new DateTime($license['expires_at']);
                $base_time->modify("+" . $add_value . " " . $add_unit);
                $new_expires_at = $base_time->format('Y-m-d H:i:s');

                // 如果是从过期状态续费，状态变回 active
                $new_status = ($license['status'] === 'expired') ? 'active' : $license['status'];

                $update_stmt = $pdo->prepare("UPDATE licenses SET expires_at = ?, status = ? WHERE id = ?");
                $update_stmt->execute([$new_expires_at, $new_status, $id]);
            }
        }
    }

    // 修改设备数量
    if ($action === 'update_devices') {
        $new_max_devices = (int)($_POST['new_max_devices'] ?? 0);

        if ($new_max_devices > 0 && $new_max_devices <= 100) {
            $stmt = $pdo->prepare("UPDATE licenses SET max_devices = ? WHERE id = ?");
            $stmt->execute([$new_max_devices, $id]);
        }
    }

    // 解绑指定设备 - 适配旧数据库结构
    if ($action === 'unbind_device') {
        $license_id = (int)($_POST['device_id'] ?? 0); // 这里实际传的是license_id

        header('Content-Type: application/json');

        if ($license_id <= 0) {
            echo json_encode(['success' => false, 'message' => '无效的激活码ID']);
            exit();
        }

        try {
            // 先查询激活码信息
            $check_stmt = $pdo->prepare("SELECT license_key, device_uid FROM licenses WHERE id = ?");
            $check_stmt->execute([$license_id]);
            $license_info = $check_stmt->fetch();

            if (!$license_info) {
                echo json_encode(['success' => false, 'message' => '激活码不存在']);
                exit();
            }

            if (empty($license_info['device_uid'])) {
                echo json_encode(['success' => false, 'message' => '该激活码未绑定设备']);
                exit();
            }

            // 解绑设备（清空device_uid，重置状态）
            $stmt = $pdo->prepare("UPDATE licenses SET device_uid = NULL, status = 'inactive', unbind_count = unbind_count + 1 WHERE id = ?");
            $result = $stmt->execute([$license_id]);

            if ($result && $stmt->rowCount() > 0) {
                echo json_encode(['success' => true, 'message' => '设备解绑成功']);
            } else {
                echo json_encode(['success' => false, 'message' => '解绑失败，请重试']);
            }
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => '解绑失败：' . $e->getMessage()]);
        }
        exit();
    }
}

// --- 处理 GET 请求 (解绑) ---
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    $action = $_GET['action'] ?? '';
    $id = (int)($_GET['id'] ?? 0);

    if ($id <= 0) {
        header('Location: ../index.php'); exit();
    }

    // 解绑设备
    if ($action === 'unbind') {
        $stmt = $pdo->prepare("UPDATE licenses SET device_uid = NULL, status = 'inactive', activated_at = NULL, last_ip = NULL, unbind_count = unbind_count + 1 WHERE id = ?");
        $stmt->execute([$id]);
    }
}

// 只有非AJAX请求才重定向
if (!isset($_POST['action']) || $_POST['action'] !== 'unbind_device') {
    header('Location: ../index.php');
    exit();
}