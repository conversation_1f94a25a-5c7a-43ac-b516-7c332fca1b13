<?php
require_once 'includes/check_login.php';
require_once 'includes/db.php';
require_once 'includes/header.php';
require_once 'includes/sidebar.php';

// 获取筛选参数
$search_key = $_GET['key'] ?? '';
$search_uid = $_GET['uid'] ?? '';
$search_ip = $_GET['ip'] ?? '';
$search_type = $_GET['type'] ?? '';
$search_status = $_GET['status'] ?? '';
$date_from = $_GET['date_from'] ?? '';
$date_to = $_GET['date_to'] ?? '';

$where_clauses = [];
$params = [];

if (!empty($search_key)) {
    $where_clauses[] = "license_key LIKE ?";
    $params[] = "%" . $search_key . "%";
}
if (!empty($search_uid)) {
    $where_clauses[] = "device_uid LIKE ?";
    $params[] = "%" . $search_uid . "%";
}
if (!empty($search_ip)) {
    $where_clauses[] = "ip_address LIKE ?";
    $params[] = "%" . $search_ip . "%";
}
if (!empty($search_type)) {
    $where_clauses[] = "log_type = ?";
    $params[] = $search_type;
}
if (!empty($search_status)) {
    if ($search_status === 'SUCCESS') {
        $where_clauses[] = "status = 'SUCCESS'";
    } else {
        $where_clauses[] = "status != 'SUCCESS'";
    }
}
if (!empty($date_from)) {
    $where_clauses[] = "DATE(log_time) >= ?";
    $params[] = $date_from;
}
if (!empty($date_to)) {
    $where_clauses[] = "DATE(log_time) <= ?";
    $params[] = $date_to;
}

// 定义状态和类型的中文映射
$status_map = [
    'SUCCESS' => ['text' => '成功', 'color' => 'success'],
    'FAILED_KEY_NOT_FOUND' => ['text' => '激活码不存在', 'color' => 'danger'],
    'FAILED_KEY_DISABLED' => ['text' => '激活码已禁用', 'color' => 'warning'],
    'FAILED_KEY_EXPIRED' => ['text' => '激活码已过期', 'color' => 'warning'],
    'FAILED_KEY_BOUND_OTHER' => ['text' => '激活码已绑定其他设备', 'color' => 'danger'],
    'FAILED_MAX_DEVICES_REACHED' => ['text' => '设备数量已达上限', 'color' => 'warning'],
    'FAILED_DEVICE_NOT_FOUND' => ['text' => '设备未找到', 'color' => 'danger'],
    'FAILED_INVALID_SKEY' => ['text' => '无效的验证密钥', 'color' => 'danger'],
    'FAILED_INVALID_STATUS' => ['text' => '无效状态', 'color' => 'danger'],
    'FAILED_INVALID_REQUEST' => ['text' => '无效请求', 'color' => 'danger'],
];

$type_map = [
    'activate' => ['text' => '激活', 'color' => 'primary'],
    'verify' => ['text' => '验证', 'color' => 'info'],
];

$sql = "SELECT * FROM logs";
if (!empty($where_clauses)) {
    $sql .= " WHERE " . implode(' AND ', $where_clauses);
}
$sql .= " ORDER BY id DESC LIMIT 200";

$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$logs = $stmt->fetchAll();
?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid"><h1 class="m-0">操作日志</h1></div>
    </section>
    <section class="content">
        <div class="container-fluid">
            <div class="card card-info card-outline">
                <div class="card-header">
                    <h3 class="card-title"><i class="fas fa-search"></i> 查询日志</h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-sm btn-secondary" onclick="clearFilters()">
                            <i class="fas fa-times"></i> 清除筛选
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div id="logFilterForm">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>激活码</label>
                                    <input type="text" id="search_key" class="form-control hot-search" placeholder="输入激活码实时搜索" value="<?= htmlspecialchars($search_key) ?>">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>设备UID</label>
                                    <input type="text" id="search_uid" class="form-control hot-search" placeholder="输入设备UID实时搜索" value="<?= htmlspecialchars($search_uid) ?>">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>IP地址</label>
                                    <input type="text" id="search_ip" class="form-control hot-search" placeholder="输入IP地址实时搜索" value="<?= htmlspecialchars($search_ip) ?>">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>操作类型</label>
                                    <select id="search_type" class="form-control hot-search">
                                        <option value="">全部类型</option>
                                        <option value="激活" <?= $search_type === 'activate' ? 'selected' : '' ?>>激活</option>
                                        <option value="验证" <?= $search_type === 'verify' ? 'selected' : '' ?>>验证</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>操作结果</label>
                                    <select id="search_status" class="form-control hot-search">
                                        <option value="">全部结果</option>
                                        <option value="成功" <?= $search_status === 'SUCCESS' ? 'selected' : '' ?>>成功</option>
                                        <option value="激活码不存在">激活码不存在</option>
                                        <option value="激活码已禁用">激活码已禁用</option>
                                        <option value="激活码已过期">激活码已过期</option>
                                        <option value="设备数量已达上限">设备数量已达上限</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>全局搜索</label>
                                    <input type="text" id="global_search" class="form-control" placeholder="搜索所有字段...">
                                    <small class="form-text text-muted">在所有列中搜索</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <button type="button" class="btn btn-secondary btn-block" onclick="clearAllFilters()">
                                        <i class="fas fa-times"></i> 清除所有筛选
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div class="text-muted small">
                                        实时搜索已启用<br>
                                        <i class="fas fa-info-circle"></i> 输入即时筛选
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-list"></i> 日志记录
                        <small class="text-muted">(显示最近200条记录)</small>
                    </h3>
                </div>
                <div class="card-body">
                    <table id="logsTable" class="table table-bordered table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th class="filterable-header" data-filter="type">
                                    类型 <i class="fas fa-filter text-muted"></i>
                                    <div class="filter-dropdown" style="display: none;">
                                        <div class="dropdown-menu show" style="position: absolute; top: 100%; left: 0; z-index: 1000;">
                                            <a class="dropdown-item filter-option" data-value="">全部类型</a>
                                            <a class="dropdown-item filter-option" data-value="激活">激活</a>
                                            <a class="dropdown-item filter-option" data-value="验证">验证</a>
                                        </div>
                                    </div>
                                </th>
                                <th>激活码</th>
                                <th>设备UID</th>
                                <th class="filterable-header" data-filter="ip">
                                    IP地址 <i class="fas fa-filter text-muted"></i>
                                    <div class="filter-dropdown" style="display: none;">
                                        <div class="dropdown-menu show" style="position: absolute; top: 100%; left: 0; z-index: 1000;">
                                            <div class="px-2 py-1">
                                                <input type="text" class="form-control form-control-sm" placeholder="搜索IP..." id="ipFilter">
                                            </div>
                                            <div class="dropdown-divider"></div>
                                            <a class="dropdown-item filter-option" data-value="">全部IP</a>
                                            <?php
                                            $unique_ips = $pdo->query("SELECT DISTINCT ip_address FROM logs WHERE ip_address IS NOT NULL ORDER BY ip_address LIMIT 20")->fetchAll();
                                            foreach ($unique_ips as $ip_row):
                                            ?>
                                            <a class="dropdown-item filter-option" data-value="<?= htmlspecialchars($ip_row['ip_address']) ?>">
                                                <?= htmlspecialchars($ip_row['ip_address']) ?>
                                            </a>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                </th>
                                <th class="filterable-header" data-filter="result">
                                    结果 <i class="fas fa-filter text-muted"></i>
                                    <div class="filter-dropdown" style="display: none;">
                                        <div class="dropdown-menu show" style="position: absolute; top: 100%; left: 0; z-index: 1000;">
                                            <a class="dropdown-item filter-option" data-value="">全部结果</a>
                                            <a class="dropdown-item filter-option" data-value="成功">成功</a>
                                            <a class="dropdown-item filter-option" data-value="激活码不存在">激活码不存在</a>
                                            <a class="dropdown-item filter-option" data-value="激活码已禁用">激活码已禁用</a>
                                            <a class="dropdown-item filter-option" data-value="激活码已过期">激活码已过期</a>
                                            <a class="dropdown-item filter-option" data-value="设备数量已达上限">设备数量已达上限</a>
                                        </div>
                                    </div>
                                </th>
                                <th>时间</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($logs as $log):
                                $type_info = $type_map[$log['log_type']] ?? ['text' => $log['log_type'], 'color' => 'secondary'];
                                $status_info = $status_map[$log['status']] ?? ['text' => $log['status'], 'color' => 'secondary'];
                            ?>
                            <tr>
                                <td><?= $log['id'] ?></td>
                                <td>
                                    <span class="badge bg-<?= $type_info['color'] ?>"><?= $type_info['text'] ?></span>
                                </td>
                                <td>
                                    <?php if ($log['license_key']): ?>
                                        <a href="?key=<?= urlencode($log['license_key']) ?>" class="text-primary">
                                            <?= htmlspecialchars($log['license_key']) ?>
                                        </a>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($log['device_uid']): ?>
                                        <a href="?uid=<?= urlencode($log['device_uid']) ?>" class="text-info">
                                            <small><?= htmlspecialchars(substr($log['device_uid'], 0, 20)) ?><?= strlen($log['device_uid']) > 20 ? '...' : '' ?></small>
                                        </a>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($log['ip_address']): ?>
                                        <a href="?ip=<?= urlencode($log['ip_address']) ?>" class="text-warning">
                                            <?= htmlspecialchars($log['ip_address']) ?>
                                        </a>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge bg-<?= $status_info['color'] ?>" title="<?= htmlspecialchars($log['status']) ?>">
                                        <?= $status_info['text'] ?>
                                    </span>
                                </td>
                                <td>
                                    <small><?= date('m-d H:i:s', strtotime($log['log_time'])) ?></small>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>

                    <?php if (empty($logs)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <p class="text-muted">没有找到符合条件的日志记录</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>
</div>

<script>
$(document).ready(function() {
    // 初始化DataTables
    var logsTable = $('#logsTable').DataTable({
        "paging": true,
        "lengthChange": true,
        "searching": true,
        "ordering": true,
        "info": true,
        "autoWidth": false,
        "responsive": true,
        "order": [[ 0, "desc" ]],
        "pageLength": 50,
        "lengthMenu": [[10, 25, 50, 100, 500, -1], [10, 25, 50, 100, 500, "全部"]],
        "language": {
            "lengthMenu": "显示 _MENU_ 条记录",
            "info": "显示第 _START_ 至 _END_ 项结果，共 _TOTAL_ 项",
            "infoEmpty": "显示第 0 至 0 项结果，共 0 项",
            "infoFiltered": "(由 _MAX_ 项结果过滤)",
            "search": "全局搜索:",
            "paginate": {
                "first": "首页",
                "last": "末页",
                "next": "下一页",
                "previous": "上一页"
            },
            "emptyTable": "没有找到符合条件的日志记录",
            "zeroRecords": "没有找到符合条件的记录"
        }
    });

    // ===================================================================
    // 热搜索功能实现
    // ===================================================================

    // 激活码搜索 (第3列)
    $('#search_key').on('keyup', function() {
        logsTable.column(2).search(this.value).draw();
    });

    // 设备UID搜索 (第4列)
    $('#search_uid').on('keyup', function() {
        logsTable.column(3).search(this.value).draw();
    });

    // IP地址搜索 (第5列)
    $('#search_ip').on('keyup', function() {
        logsTable.column(4).search(this.value).draw();
    });

    // 操作类型筛选 (第2列)
    $('#search_type').on('change', function() {
        logsTable.column(1).search(this.value).draw();
    });

    // 操作结果筛选 (第6列)
    $('#search_status').on('change', function() {
        logsTable.column(5).search(this.value).draw();
    });

    // 全局搜索
    $('#global_search').on('keyup', function() {
        logsTable.search(this.value).draw();
    });

    // 表头筛选功能（复用index.php的逻辑）
    $('.filterable-header').on('click', function(e) {
        e.stopPropagation();
        var $this = $(this);
        var $dropdown = $this.find('.filter-dropdown');

        $('.filter-dropdown').not($dropdown).hide();
        $dropdown.toggle();
    });

    $(document).on('click', '.filter-option', function(e) {
        e.preventDefault();
        var $this = $(this);
        var filterValue = $this.data('value');
        var $header = $this.closest('.filterable-header');
        var filterType = $header.data('filter');

        applyLogFilter(filterType, filterValue);
        $header.find('.filter-dropdown').hide();
        updateLogFilterHeader($header, filterValue);
    });

    // IP搜索功能
    $('#ipFilter').on('input', function() {
        var searchText = $(this).val().toLowerCase();
        $(this).closest('.dropdown-menu').find('.filter-option').each(function() {
            var optionText = $(this).text().toLowerCase();
            $(this).toggle(optionText.includes(searchText));
        });
    });

    $(document).on('click', function() {
        $('.filter-dropdown').hide();
    });

    function applyLogFilter(filterType, filterValue) {
        var columnIndex;
        switch(filterType) {
            case 'type':
                columnIndex = 1;
                break;
            case 'ip':
                columnIndex = 4;
                break;
            case 'result':
                columnIndex = 5;
                break;
            default:
                return;
        }
        logsTable.column(columnIndex).search(filterValue).draw();
    }

    function updateLogFilterHeader($header, filterValue) {
        var $icon = $header.find('i');
        if (filterValue) {
            $icon.removeClass('text-muted').addClass('text-primary');
            $header.addClass('filtered');
        } else {
            $icon.removeClass('text-primary').addClass('text-muted');
            $header.removeClass('filtered');
        }
    }
});

// 清除所有筛选
function clearAllFilters() {
    // 清除所有搜索框
    $('#search_key, #search_uid, #search_ip, #global_search').val('');
    $('#search_type, #search_status').val('');

    // 清除DataTables的所有搜索
    logsTable.search('').columns().search('').draw();

    // 清除表头筛选状态
    $('.filterable-header').removeClass('filtered');
    $('.filterable-header i').removeClass('text-primary').addClass('text-muted');
}
</script>

<?php require_once 'includes/footer.php'; ?>