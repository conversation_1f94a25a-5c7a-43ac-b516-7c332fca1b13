<?php
require_once 'includes/check_login.php';
require_once 'includes/db.php';
require_once 'includes/header.php';
require_once 'includes/sidebar.php';

$search_key = $_GET['key'] ?? '';
$search_uid = $_GET['uid'] ?? '';
$where_clauses = [];
$params = [];

if (!empty($search_key)) {
    $where_clauses[] = "license_key LIKE ?";
    $params[] = "%" . $search_key . "%";
}
if (!empty($search_uid)) {
    $where_clauses[] = "device_uid LIKE ?";
    $params[] = "%" . $search_uid . "%";
}

$sql = "SELECT * FROM logs";
if (!empty($where_clauses)) {
    $sql .= " WHERE " . implode(' AND ', $where_clauses);
}
$sql .= " ORDER BY id DESC LIMIT 200";

$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$logs = $stmt->fetchAll();
?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid"><h1 class="m-0">操作日志</h1></div>
    </section>
    <section class="content">
        <div class="container-fluid">
            <div class="card card-info card-outline">
                <div class="card-header"><h3 class="card-title"><i class="fas fa-search"></i> 查询日志</h3></div>
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-5"><input type="text" name="key" class="form-control" placeholder="按激活码查询" value="<?= htmlspecialchars($search_key) ?>"></div>
                        <div class="col-md-5"><input type="text" name="uid" class="form-control" placeholder="按设备UID查询" value="<?= htmlspecialchars($search_uid) ?>"></div>
                        <div class="col-md-2"><button type="submit" class="btn btn-info w-100">查询</button></div>
                    </form>
                </div>
            </div>
            <div class="card">
                <div class="card-body">
                    <table class="table table-bordered table-hover">
                        <thead>
                            <tr><th>ID</th><th>类型</th><th>激活码</th><th>设备UID</th><th>IP地址</th><th>结果</th><th>时间</th></tr>
                        </thead>
                        <tbody>
                            <?php foreach ($logs as $log): ?>
                            <tr>
                                <td><?= $log['id'] ?></td>
                                <td><span class="badge bg-<?= $log['log_type'] == 'activate' ? 'primary' : 'secondary' ?>"><?= $log['log_type'] ?></span></td>
                                <td><a href="?key=<?= urlencode($log['license_key']) ?>"><?= htmlspecialchars($log['license_key']) ?></a></td>
                                <td><a href="?uid=<?= urlencode($log['device_uid']) ?>"><?= htmlspecialchars($log['device_uid']) ?></a></td>
                                <td><?= htmlspecialchars($log['ip_address']) ?></td>
                                <td><span class="badge bg-<?= strpos($log['status'], 'SUCCESS') !== false ? 'success' : 'danger' ?>"><?= htmlspecialchars($log['status']) ?></span></td>
                                <td><?= $log['log_time'] ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </section>
</div>

<?php require_once 'includes/footer.php'; ?>