<?php
require_once 'includes/check_login.php';
require_once 'includes/db.php';
require_once 'includes/header.php';
require_once 'includes/sidebar.php';

// 获取筛选参数
$search_key = $_GET['key'] ?? '';
$search_uid = $_GET['uid'] ?? '';
$search_ip = $_GET['ip'] ?? '';
$search_type = $_GET['type'] ?? '';
$search_status = $_GET['status'] ?? '';
$date_from = $_GET['date_from'] ?? '';
$date_to = $_GET['date_to'] ?? '';

$where_clauses = [];
$params = [];

if (!empty($search_key)) {
    $where_clauses[] = "license_key LIKE ?";
    $params[] = "%" . $search_key . "%";
}
if (!empty($search_uid)) {
    $where_clauses[] = "device_uid LIKE ?";
    $params[] = "%" . $search_uid . "%";
}
if (!empty($search_ip)) {
    $where_clauses[] = "ip_address LIKE ?";
    $params[] = "%" . $search_ip . "%";
}
if (!empty($search_type)) {
    $where_clauses[] = "log_type = ?";
    $params[] = $search_type;
}
if (!empty($search_status)) {
    if ($search_status === 'SUCCESS') {
        $where_clauses[] = "status = 'SUCCESS'";
    } else {
        $where_clauses[] = "status != 'SUCCESS'";
    }
}
if (!empty($date_from)) {
    $where_clauses[] = "DATE(log_time) >= ?";
    $params[] = $date_from;
}
if (!empty($date_to)) {
    $where_clauses[] = "DATE(log_time) <= ?";
    $params[] = $date_to;
}

// 定义状态和类型的中文映射
$status_map = [
    'SUCCESS' => ['text' => '成功', 'color' => 'success'],
    'FAILED_KEY_NOT_FOUND' => ['text' => '激活码不存在', 'color' => 'danger'],
    'FAILED_KEY_DISABLED' => ['text' => '激活码已禁用', 'color' => 'warning'],
    'FAILED_KEY_EXPIRED' => ['text' => '激活码已过期', 'color' => 'warning'],
    'FAILED_KEY_BOUND_OTHER' => ['text' => '激活码已绑定其他设备', 'color' => 'danger'],
    'FAILED_MAX_DEVICES_REACHED' => ['text' => '设备数量已达上限', 'color' => 'warning'],
    'FAILED_DEVICE_NOT_FOUND' => ['text' => '设备未找到', 'color' => 'danger'],
    'FAILED_INVALID_SKEY' => ['text' => '无效的验证密钥', 'color' => 'danger'],
    'FAILED_INVALID_STATUS' => ['text' => '无效状态', 'color' => 'danger'],
    'FAILED_INVALID_REQUEST' => ['text' => '无效请求', 'color' => 'danger'],
];

$type_map = [
    'activate' => ['text' => '激活', 'color' => 'primary'],
    'verify' => ['text' => '验证', 'color' => 'info'],
];

$sql = "SELECT * FROM logs";
if (!empty($where_clauses)) {
    $sql .= " WHERE " . implode(' AND ', $where_clauses);
}
$sql .= " ORDER BY id DESC LIMIT 200";

$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$logs = $stmt->fetchAll();
?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid"><h1 class="m-0">操作日志</h1></div>
    </section>
    <section class="content">
        <div class="container-fluid">
            <div class="card card-info card-outline">
                <div class="card-header">
                    <h3 class="card-title"><i class="fas fa-search"></i> 查询日志</h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-sm btn-secondary" onclick="clearFilters()">
                            <i class="fas fa-times"></i> 清除筛选
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <form method="get" id="logFilterForm">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>激活码</label>
                                    <input type="text" name="key" class="form-control" placeholder="按激活码查询" value="<?= htmlspecialchars($search_key) ?>">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>设备UID</label>
                                    <input type="text" name="uid" class="form-control" placeholder="按设备UID查询" value="<?= htmlspecialchars($search_uid) ?>">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>IP地址</label>
                                    <input type="text" name="ip" class="form-control" placeholder="按IP地址查询" value="<?= htmlspecialchars($search_ip) ?>">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>操作类型</label>
                                    <select name="type" class="form-control">
                                        <option value="">全部类型</option>
                                        <option value="activate" <?= $search_type === 'activate' ? 'selected' : '' ?>>激活</option>
                                        <option value="verify" <?= $search_type === 'verify' ? 'selected' : '' ?>>验证</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>操作结果</label>
                                    <select name="status" class="form-control">
                                        <option value="">全部结果</option>
                                        <option value="SUCCESS" <?= $search_status === 'SUCCESS' ? 'selected' : '' ?>>成功</option>
                                        <option value="FAILED" <?= $search_status === 'FAILED' ? 'selected' : '' ?>>失败</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>开始日期</label>
                                    <input type="date" name="date_from" class="form-control" value="<?= htmlspecialchars($date_from) ?>">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>结束日期</label>
                                    <input type="date" name="date_to" class="form-control" value="<?= htmlspecialchars($date_to) ?>">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <button type="submit" class="btn btn-info btn-block">
                                        <i class="fas fa-search"></i> 查询日志
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-list"></i> 日志记录
                        <small class="text-muted">(显示最近200条记录)</small>
                    </h3>
                </div>
                <div class="card-body">
                    <table id="logsTable" class="table table-bordered table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th class="filterable-header" data-filter="type">
                                    类型 <i class="fas fa-filter text-muted"></i>
                                    <div class="filter-dropdown" style="display: none;">
                                        <div class="dropdown-menu show" style="position: absolute; top: 100%; left: 0; z-index: 1000;">
                                            <a class="dropdown-item filter-option" data-value="">全部类型</a>
                                            <a class="dropdown-item filter-option" data-value="激活">激活</a>
                                            <a class="dropdown-item filter-option" data-value="验证">验证</a>
                                        </div>
                                    </div>
                                </th>
                                <th>激活码</th>
                                <th>设备UID</th>
                                <th class="filterable-header" data-filter="ip">
                                    IP地址 <i class="fas fa-filter text-muted"></i>
                                    <div class="filter-dropdown" style="display: none;">
                                        <div class="dropdown-menu show" style="position: absolute; top: 100%; left: 0; z-index: 1000;">
                                            <div class="px-2 py-1">
                                                <input type="text" class="form-control form-control-sm" placeholder="搜索IP..." id="ipFilter">
                                            </div>
                                            <div class="dropdown-divider"></div>
                                            <a class="dropdown-item filter-option" data-value="">全部IP</a>
                                            <?php
                                            $unique_ips = $pdo->query("SELECT DISTINCT ip_address FROM logs WHERE ip_address IS NOT NULL ORDER BY ip_address LIMIT 20")->fetchAll();
                                            foreach ($unique_ips as $ip_row):
                                            ?>
                                            <a class="dropdown-item filter-option" data-value="<?= htmlspecialchars($ip_row['ip_address']) ?>">
                                                <?= htmlspecialchars($ip_row['ip_address']) ?>
                                            </a>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                </th>
                                <th class="filterable-header" data-filter="result">
                                    结果 <i class="fas fa-filter text-muted"></i>
                                    <div class="filter-dropdown" style="display: none;">
                                        <div class="dropdown-menu show" style="position: absolute; top: 100%; left: 0; z-index: 1000;">
                                            <a class="dropdown-item filter-option" data-value="">全部结果</a>
                                            <a class="dropdown-item filter-option" data-value="成功">成功</a>
                                            <a class="dropdown-item filter-option" data-value="激活码不存在">激活码不存在</a>
                                            <a class="dropdown-item filter-option" data-value="激活码已禁用">激活码已禁用</a>
                                            <a class="dropdown-item filter-option" data-value="激活码已过期">激活码已过期</a>
                                            <a class="dropdown-item filter-option" data-value="设备数量已达上限">设备数量已达上限</a>
                                        </div>
                                    </div>
                                </th>
                                <th>时间</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($logs as $log):
                                $type_info = $type_map[$log['log_type']] ?? ['text' => $log['log_type'], 'color' => 'secondary'];
                                $status_info = $status_map[$log['status']] ?? ['text' => $log['status'], 'color' => 'secondary'];
                            ?>
                            <tr>
                                <td><?= $log['id'] ?></td>
                                <td>
                                    <span class="badge bg-<?= $type_info['color'] ?>"><?= $type_info['text'] ?></span>
                                </td>
                                <td>
                                    <?php if ($log['license_key']): ?>
                                        <a href="?key=<?= urlencode($log['license_key']) ?>" class="text-primary">
                                            <?= htmlspecialchars($log['license_key']) ?>
                                        </a>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($log['device_uid']): ?>
                                        <a href="?uid=<?= urlencode($log['device_uid']) ?>" class="text-info">
                                            <small><?= htmlspecialchars(substr($log['device_uid'], 0, 20)) ?><?= strlen($log['device_uid']) > 20 ? '...' : '' ?></small>
                                        </a>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($log['ip_address']): ?>
                                        <a href="?ip=<?= urlencode($log['ip_address']) ?>" class="text-warning">
                                            <?= htmlspecialchars($log['ip_address']) ?>
                                        </a>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge bg-<?= $status_info['color'] ?>" title="<?= htmlspecialchars($log['status']) ?>">
                                        <?= $status_info['text'] ?>
                                    </span>
                                </td>
                                <td>
                                    <small><?= date('m-d H:i:s', strtotime($log['log_time'])) ?></small>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>

                    <?php if (empty($logs)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <p class="text-muted">没有找到符合条件的日志记录</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>
</div>

<script>
$(document).ready(function() {
    // 初始化DataTables
    var logsTable = $('#logsTable').DataTable({
        "paging": true,
        "lengthChange": true,
        "searching": true,
        "ordering": true,
        "info": true,
        "autoWidth": false,
        "responsive": true,
        "order": [[ 0, "desc" ]],
        "pageLength": 50,
        "language": {
            "url": "assets/json/zh-Hans.json"
        }
    });

    // 表头筛选功能（复用index.php的逻辑）
    $('.filterable-header').on('click', function(e) {
        e.stopPropagation();
        var $this = $(this);
        var $dropdown = $this.find('.filter-dropdown');

        $('.filter-dropdown').not($dropdown).hide();
        $dropdown.toggle();
    });

    $(document).on('click', '.filter-option', function(e) {
        e.preventDefault();
        var $this = $(this);
        var filterValue = $this.data('value');
        var $header = $this.closest('.filterable-header');
        var filterType = $header.data('filter');

        applyLogFilter(filterType, filterValue);
        $header.find('.filter-dropdown').hide();
        updateLogFilterHeader($header, filterValue);
    });

    // IP搜索功能
    $('#ipFilter').on('input', function() {
        var searchText = $(this).val().toLowerCase();
        $(this).closest('.dropdown-menu').find('.filter-option').each(function() {
            var optionText = $(this).text().toLowerCase();
            $(this).toggle(optionText.includes(searchText));
        });
    });

    $(document).on('click', function() {
        $('.filter-dropdown').hide();
    });

    function applyLogFilter(filterType, filterValue) {
        var columnIndex;
        switch(filterType) {
            case 'type':
                columnIndex = 1;
                break;
            case 'ip':
                columnIndex = 4;
                break;
            case 'result':
                columnIndex = 5;
                break;
            default:
                return;
        }
        logsTable.column(columnIndex).search(filterValue).draw();
    }

    function updateLogFilterHeader($header, filterValue) {
        var $icon = $header.find('i');
        if (filterValue) {
            $icon.removeClass('text-muted').addClass('text-primary');
            $header.addClass('filtered');
        } else {
            $icon.removeClass('text-primary').addClass('text-muted');
            $header.removeClass('filtered');
        }
    }
});

// 清除筛选
function clearFilters() {
    document.getElementById('logFilterForm').reset();
    window.location.href = 'logs.php';
}
</script>

<?php require_once 'includes/footer.php'; ?>