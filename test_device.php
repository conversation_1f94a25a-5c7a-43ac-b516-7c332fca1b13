<?php
require_once 'includes/check_login.php';
require_once 'includes/header.php';
require_once 'includes/sidebar.php';
?>

<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <h1 class="m-0">设备测试工具</h1>
            <p class="text-muted">模拟设备发送激活和验证请求，用于测试系统功能</p>
        </div>
    </section>
    
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <!-- 激活测试 -->
                <div class="col-md-6">
                    <div class="card card-primary">
                        <div class="card-header">
                            <h3 class="card-title"><i class="fas fa-key"></i> 激活测试</h3>
                        </div>
                        <div class="card-body">
                            <form id="activateForm">
                                <div class="form-group">
                                    <label for="activate_key">激活码</label>
                                    <input type="text" class="form-control" id="activate_key" placeholder="输入激活码" required>
                                </div>
                                <div class="form-group">
                                    <label for="activate_uid">设备UID</label>
                                    <input type="text" class="form-control" id="activate_uid" placeholder="设备唯一标识" required>
                                    <small class="form-text text-muted">
                                        <button type="button" class="btn btn-sm btn-link p-0" onclick="generateRandomUID('activate_uid')">生成随机UID</button>
                                    </small>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-play"></i> 测试激活
                                </button>
                            </form>
                            
                            <div class="mt-3">
                                <h6>激活结果：</h6>
                                <div id="activateResult" class="border p-2 bg-light" style="min-height: 100px; font-family: monospace;">
                                    等待测试...
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 验证测试 -->
                <div class="col-md-6">
                    <div class="card card-success">
                        <div class="card-header">
                            <h3 class="card-title"><i class="fas fa-shield-alt"></i> 验证测试</h3>
                        </div>
                        <div class="card-body">
                            <form id="verifyForm">
                                <div class="form-group">
                                    <label for="verify_skeys">Skeys</label>
                                    <textarea class="form-control" id="verify_skeys" rows="3" placeholder="从激活结果中复制Skeys" required></textarea>
                                </div>
                                <div class="form-group">
                                    <label for="verify_uid">设备UID</label>
                                    <input type="text" class="form-control" id="verify_uid" placeholder="设备唯一标识" required>
                                    <small class="form-text text-muted">
                                        <button type="button" class="btn btn-sm btn-link p-0" onclick="generateRandomUID('verify_uid')">生成随机UID</button>
                                    </small>
                                </div>
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-check"></i> 测试验证
                                </button>
                            </form>
                            
                            <div class="mt-3">
                                <h6>验证结果：</h6>
                                <div id="verifyResult" class="border p-2 bg-light" style="min-height: 100px; font-family: monospace;">
                                    等待测试...
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 批量测试 -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card card-warning">
                        <div class="card-header">
                            <h3 class="card-title"><i class="fas fa-tasks"></i> 批量测试</h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="batch_key">激活码</label>
                                        <input type="text" class="form-control" id="batch_key" placeholder="输入激活码">
                                    </div>
                                    <div class="form-group">
                                        <label for="batch_count">设备数量</label>
                                        <input type="number" class="form-control" id="batch_count" value="3" min="1" max="10">
                                        <small class="form-text text-muted">模拟多个设备同时激活</small>
                                    </div>
                                    <button type="button" class="btn btn-warning" onclick="batchTest()">
                                        <i class="fas fa-rocket"></i> 开始批量测试
                                    </button>
                                </div>
                                <div class="col-md-6">
                                    <h6>批量测试结果：</h6>
                                    <div id="batchResult" class="border p-2 bg-light" style="height: 200px; overflow-y: auto; font-family: monospace;">
                                        等待测试...
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 测试说明 -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card card-info">
                        <div class="card-header">
                            <h3 class="card-title"><i class="fas fa-info-circle"></i> 测试说明</h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>激活流程：</h6>
                                    <ol>
                                        <li>输入有效的激活码</li>
                                        <li>输入设备UID（可生成随机）</li>
                                        <li>点击"测试激活"</li>
                                        <li>成功后会返回Skeys</li>
                                    </ol>
                                </div>
                                <div class="col-md-6">
                                    <h6>验证流程：</h6>
                                    <ol>
                                        <li>将激活返回的Skeys复制到验证框</li>
                                        <li>输入相同的设备UID</li>
                                        <li>点击"测试验证"</li>
                                        <li>成功返回"VALID"</li>
                                    </ol>
                                </div>
                            </div>
                            <div class="alert alert-warning mt-3">
                                <i class="fas fa-exclamation-triangle"></i>
                                <strong>注意：</strong> 此工具仅用于测试，请勿在生产环境中频繁使用。
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<script>
// 生成随机UID
function generateRandomUID(inputId) {
    const uid = 'TEST_' + Math.random().toString(36).substr(2, 9).toUpperCase() + '_' + Date.now().toString(36).toUpperCase();
    document.getElementById(inputId).value = uid;
}

// 激活测试
document.getElementById('activateForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const key = document.getElementById('activate_key').value;
    const uid = document.getElementById('activate_uid').value;
    const resultDiv = document.getElementById('activateResult');
    
    resultDiv.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 正在测试激活...';
    
    const formData = new FormData();
    formData.append('type', 'activate');
    formData.append('uks', key);
    formData.append('uid', uid);
    
    fetch('api/validate.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.text())
    .then(data => {
        const timestamp = new Date().toLocaleTimeString();
        if (data.length > 10 && !data.includes('FAILED') && !data.includes('ERROR')) {
            resultDiv.innerHTML = `<div class="text-success">[${timestamp}] 激活成功！</div><div class="mt-2"><strong>Skeys:</strong><br><textarea class="form-control" rows="3" readonly>${data}</textarea></div><button class="btn btn-sm btn-primary mt-2" onclick="copyToVerify('${data}', '${uid}')">复制到验证测试</button>`;
        } else {
            resultDiv.innerHTML = `<div class="text-danger">[${timestamp}] 激活失败：${data}</div>`;
        }
    })
    .catch(error => {
        resultDiv.innerHTML = `<div class="text-danger">请求失败：${error}</div>`;
    });
});

// 验证测试
document.getElementById('verifyForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const skeys = document.getElementById('verify_skeys').value;
    const uid = document.getElementById('verify_uid').value;
    const resultDiv = document.getElementById('verifyResult');
    
    resultDiv.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 正在测试验证...';
    
    const formData = new FormData();
    formData.append('type', 'verify');
    formData.append('skeys', skeys);
    formData.append('uid', uid);
    
    fetch('api/validate.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.text())
    .then(data => {
        const timestamp = new Date().toLocaleTimeString();
        if (data === 'VALID') {
            resultDiv.innerHTML = `<div class="text-success">[${timestamp}] 验证成功：${data}</div>`;
        } else {
            resultDiv.innerHTML = `<div class="text-danger">[${timestamp}] 验证失败：${data}</div>`;
        }
    })
    .catch(error => {
        resultDiv.innerHTML = `<div class="text-danger">请求失败：${error}</div>`;
    });
});

// 复制到验证测试
function copyToVerify(skeys, uid) {
    document.getElementById('verify_skeys').value = skeys;
    document.getElementById('verify_uid').value = uid;
    
    // 滚动到验证区域
    document.querySelector('.card-success').scrollIntoView({ behavior: 'smooth' });
}

// 批量测试
function batchTest() {
    const key = document.getElementById('batch_key').value;
    const count = parseInt(document.getElementById('batch_count').value);
    const resultDiv = document.getElementById('batchResult');
    
    if (!key) {
        alert('请输入激活码');
        return;
    }
    
    resultDiv.innerHTML = '<div class="text-info">开始批量测试...</div>';
    
    for (let i = 1; i <= count; i++) {
        const uid = `BATCH_${i}_${Date.now()}`;
        
        setTimeout(() => {
            const formData = new FormData();
            formData.append('type', 'activate');
            formData.append('uks', key);
            formData.append('uid', uid);
            
            fetch('api/validate.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(data => {
                const timestamp = new Date().toLocaleTimeString();
                const status = (data.length > 10 && !data.includes('FAILED')) ? 'success' : 'danger';
                const message = (status === 'success') ? '成功' : data;
                
                resultDiv.innerHTML += `<div class="text-${status}">[${timestamp}] 设备${i} (${uid.substr(0, 20)}...): ${message}</div>`;
                resultDiv.scrollTop = resultDiv.scrollHeight;
            })
            .catch(error => {
                resultDiv.innerHTML += `<div class="text-danger">设备${i} 请求失败：${error}</div>`;
            });
        }, i * 500); // 每500ms发送一个请求
    }
}

// 页面加载时生成默认UID
document.addEventListener('DOMContentLoaded', function() {
    generateRandomUID('activate_uid');
    generateRandomUID('verify_uid');
});
</script>

<?php require_once 'includes/footer.php'; ?>
