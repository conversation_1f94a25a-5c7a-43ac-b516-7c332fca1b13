<?php
require_once 'includes/check_login.php';
require_once 'includes/db.php';
require_once 'includes/header.php';
require_once 'includes/sidebar.php';

// (此部分PHP逻辑不变)
$filter_plan = (int)($_GET['plan'] ?? 0);
$where_clauses = [];
$params = [];
if ($filter_plan > 0) {
    $where_clauses[] = "l.plan_id = ?";
    $params[] = $filter_plan;
}
// 获取基础统计信息
$stats = $pdo->query("
    SELECT
        (SELECT COUNT(*) FROM licenses) as total,
        (SELECT COUNT(*) FROM licenses WHERE status = 'active') as active,
        (SELECT COUNT(*) FROM licenses WHERE status = 'inactive') as inactive,
        (SELECT COUNT(*) FROM licenses WHERE status = 'expired') as expired,
        (SELECT COUNT(*) FROM licenses WHERE status = 'disabled') as disabled
")->fetch();

// 获取今日统计信息
$today = date('Y-m-d');
$today_stats = $pdo->query("
    SELECT
        (SELECT COUNT(*) FROM logs WHERE DATE(log_time) = '$today' AND log_type = 'activate' AND status = 'SUCCESS') as today_activations,
        (SELECT COUNT(*) FROM logs WHERE DATE(log_time) = '$today' AND log_type = 'verify' AND status = 'SUCCESS') as today_verifications,
        (SELECT COUNT(DISTINCT device_uid) FROM logs WHERE DATE(log_time) = '$today' AND status = 'SUCCESS') as today_unique_devices,
        (SELECT COUNT(*) FROM license_devices WHERE DATE(activated_at) = '$today') as today_new_devices,
        (SELECT COUNT(DISTINCT license_key) FROM logs WHERE DATE(log_time) = '$today' AND log_type = 'activate' AND status = 'SUCCESS') as today_activated_keys
")->fetch();

// 获取设备统计信息
$device_stats = $pdo->query("
    SELECT
        (SELECT COUNT(*) FROM license_devices WHERE status = 'active') as total_devices,
        (SELECT COUNT(DISTINCT license_id) FROM license_devices WHERE status = 'active') as licenses_with_devices,
        (SELECT AVG(verify_count) FROM license_devices WHERE status = 'active') as avg_verifications
")->fetch();
$sql = "SELECT l.*, p.plan_name, p.max_devices FROM licenses l JOIN plans p ON l.plan_id = p.id";
if (!empty($where_clauses)) {
    $sql .= " WHERE " . implode(' AND ', $where_clauses);
}
$sql .= " ORDER BY l.id DESC";
$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$licenses = $stmt->fetchAll();

// 为每个激活码获取绑定的设备信息
foreach ($licenses as &$license) {
    $device_stmt = $pdo->prepare("SELECT device_uid, activated_at, last_verify_at, verify_count, status FROM license_devices WHERE license_id = ? ORDER BY activated_at DESC");
    $device_stmt->execute([$license['id']]);
    $license['devices'] = $device_stmt->fetchAll();
}
$status_map = [
    'active' => ['text' => '已激活', 'color' => 'success'],
    'inactive' => ['text' => '未使用', 'color' => 'info'],
    'expired' => ['text' => '已过期', 'color' => 'warning'],
    'disabled' => ['text' => '已禁用', 'color' => 'danger'],
];
?>

<!-- Content Wrapper. Contains page content -->
<div class="content-wrapper">
    <!-- (顶部内容不变) -->
    <div class="content-header"><div class="container-fluid"><h1 class="m-0">仪表盘</h1></div></div>
    <section class="content"><div class="container-fluid">
    <div class="row">
        <!-- (Info boxes 不变) -->
        <div class="col-12 col-sm-6 col-md-3"><div class="info-box"><span class="info-box-icon bg-primary elevation-1"><i class="fas fa-hdd"></i></span><div class="info-box-content"><span class="info-box-text">总激活码</span><span class="info-box-number"><?= $stats['total'] ?></span></div></div></div>
        <div class="col-12 col-sm-6 col-md-3"><div class="info-box mb-3"><span class="info-box-icon bg-success elevation-1"><i class="fas fa-check-circle"></i></span><div class="info-box-content"><span class="info-box-text">已激活</span><span class="info-box-number"><?= $stats['active'] ?></span></div></div></div>
        <div class="clearfix hidden-md-up"></div>
        <div class="col-12 col-sm-6 col-md-3"><div class="info-box mb-3"><span class="info-box-icon bg-info elevation-1"><i class="fas fa-pause-circle"></i></span><div class="info-box-content"><span class="info-box-text">未使用</span><span class="info-box-number"><?= $stats['inactive'] ?></span></div></div></div>
        <div class="col-12 col-sm-6 col-md-3"><div class="info-box mb-3"><span class="info-box-icon bg-danger elevation-1"><i class="fas fa-times-circle"></i></span><div class="info-box-content"><span class="info-box-text">已禁用</span><span class="info-box-number"><?= $stats['disabled'] ?></span></div></div></div>
    </div>
    <div class="card card-primary card-outline">
        <!-- (生成激活码表单不变) -->
        <div class="card-header"><h3 class="card-title"><i class="fas fa-key"></i> 生成激活码</h3></div>
        <div class="card-body">
    <form id="generateKeysForm" action="actions/generate_keys.php" method="post" class="row g-3 align-items-end">
        <!-- 第一行 -->
        <div class="col-md-4">
            <label for="plan_id_gen" class="form-label">选择套餐:</label>
            <select name="plan_id" id="plan_id_gen" class="form-control" required>
                <?php 
                // 从数据库获取可用的套餐
                $gen_plans = $pdo->query("SELECT * FROM plans WHERE is_active = 1 ORDER BY id")->fetchAll();
                foreach ($gen_plans as $plan):
                ?>
                <option value="<?= $plan['id'] ?>">
                    <?= htmlspecialchars($plan['plan_name']) ?>
                    (最大<?= $plan['max_devices'] ?>设备)
                </option>
                <?php endforeach; ?>
            </select>
        </div>
        
        <!-- ★★★ 新增：自定义前缀输入框 ★★★ -->
        <div class="col-md-3">
            <label for="prefix" class="form-label">自定义前缀 (可选):</label>
            <input type="text" name="prefix" id="prefix" class="form-control" placeholder="例如: TP-" value="TP-">
        </div>

        <div class="col-md-1">
            <label for="count" class="form-label">数量:</label>
            <input type="number" name="count" id="count" class="form-control" value="1" required>
        </div>

        <div class="col-md-2">
            <label for="notes_gen" class="form-label">备注 (可选):</label>
            <input type="text" name="notes" id="notes_gen" class="form-control">
        </div>
        
        <div class="col-md-2">
            <button type="submit" class="btn btn-primary w-100">生成</button>
        </div>
    </form>
</div>
    </div>
    <div class="card card-primary card-outline">
        <!-- (卡片头部和筛选器不变) -->
        <div class="card-header">
            <h3 class="card-title"><i class="fas fa-list-alt"></i> 激活码列表</h3>
            <div class="card-tools">
                <form method="get" class="d-inline-block">
                    <div class="input-group input-group-sm" style="width: 250px;">
                        <select name="plan" class="form-control" onchange="this.form.submit()">
                            <option value="">-- 按可用套餐筛选 --</option>
                            <?php $available_plans_query = "SELECT p.id, p.plan_name FROM plans p WHERE p.is_active = 1 AND p.id IN (SELECT DISTINCT plan_id FROM licenses) ORDER BY p.id ASC"; $available_plans = $pdo->query($available_plans_query)->fetchAll(); $filter_plan = $_GET['plan'] ?? ''; foreach ($available_plans as $plan): ?>
                            <option value="<?= $plan['id'] ?>" <?= $filter_plan == $plan['id'] ? 'selected' : '' ?>><?= htmlspecialchars($plan['plan_name']) ?></option>
                            <?php endforeach; ?>
                        </select>
                        <div class="input-group-append"><a href="index.php" class="btn btn-outline-secondary" title="清除筛选"><i class="fas fa-times"></i></a></div>
                    </div>
                </form>
                <div class="btn-group ml-2">
    <button type="button" class="btn btn-sm btn-info" id="copySelectedBtn"><i class="fas fa-copy"></i> 批量复制</button>
    <button type="button" class="btn btn-sm btn-danger" id="deleteSelectedBtn"><i class="fas fa-trash"></i> 批量删除</button>
    <!-- ★★★ 在这里添加调试按钮 ★★★ -->
    <button type="button" class="btn btn-sm btn-warning" id="debugDeleteBtn"><i class="fas fa-bug"></i> 调试删除</button>
</div>
            </div>
        </div>
        <div class="card-body">
            <!-- ★★★ 核心修正：移除了外围的<form>标签 ★★★ -->
            <table id="licensesTable" class="table table-bordered table-striped">
                <thead>
                    <tr>
                        <th class="no-sort"><input type="checkbox" id="selectAll"></th>
                        <th>ID</th><th>激活码</th><th>套餐</th><th>状态</th><th>绑定设备</th>
                        <th>激活时间</th><th>到期时间</th><th>备注</th><th class="no-sort" style="width: 150px;">操作</th>
                    </tr>
                </thead>
               <tbody>
    <?php foreach ($licenses as $license_item): // ★ 使用一个全新的循环变量名，避免任何可能的冲突
        $status_info = $status_map[$license_item['status']];
        $activated_display = $license_item['activated_at'] ? date('Y-m-d H:i', strtotime($license_item['activated_at'])) : '<i class="text-muted">--</i>';
        $expires_display = 'N/A';
        if ($license_item['expires_at']) {
            $expires_display = date('Y-m-d H:i', strtotime($license_item['expires_at']));
            if (new DateTime() > new DateTime($license_item['expires_at']) && $license_item['status'] === 'active') {
                $status_info = $status_map['expired'];
            }
        }
    ?>
    <tr data-id="<?= $license_item['id'] ?>">
        <!-- ★★★ 确保 value 的值是来自新的循环变量 ★★★ -->
        <td><input type="checkbox" class="license-checkbox" value="<?= $license_item['id'] ?>"></td>
        <td><?= $license_item['id'] ?></td>
        <td class="license-key-cell"><?= htmlspecialchars($license_item['license_key']) ?></td>
        <td class="plan-name-cell">
            <?= htmlspecialchars($license_item['plan_name']) ?>
            <small class="text-muted">(最大<?= $license_item['max_devices'] ?>设备)</small>
        </td>
        <td><span class="badge bg-<?= $status_info['color'] ?>"><?= $status_info['text'] ?></span></td>
        <td>
            <?php if (empty($license_item['devices'])): ?>
                <i class="text-muted">未绑定设备</i>
            <?php else: ?>
                <div class="device-list">
                    <?php foreach ($license_item['devices'] as $device): ?>
                        <div class="device-item mb-1">
                            <small class="d-block">
                                <strong>设备:</strong> <?= htmlspecialchars(substr($device['device_uid'], 0, 12)) ?>...
                                <span class="badge badge-<?= $device['status'] === 'active' ? 'success' : 'secondary' ?> ml-1">
                                    <?= $device['status'] === 'active' ? '活跃' : '禁用' ?>
                                </span>
                            </small>
                            <small class="text-muted d-block">
                                验证<?= $device['verify_count'] ?>次 |
                                最后: <?= $device['last_verify_at'] ? date('m-d H:i', strtotime($device['last_verify_at'])) : '未验证' ?>
                            </small>
                        </div>
                    <?php endforeach; ?>
                    <small class="text-info">
                        已绑定 <?= count($license_item['devices']) ?>/<?= $license_item['max_devices'] ?> 设备
                    </small>
                </div>
            <?php endif; ?>
        </td>
        <td><?= $activated_display ?></td>
        <td><?= $expires_display ?></td>
        <td class="notes-cell" data-original-text="<?= htmlspecialchars($license_item['notes']) ?>">
            <span class="notes-text"><?= htmlspecialchars($license_item['notes']) ?></span>
            <input type="text" class="form-control form-control-sm notes-input" style="display:none;" value="<?= htmlspecialchars($license_item['notes']) ?>">
        </td>
        <td>
            <div class="btn-group">
                <button class="btn btn-sm btn-secondary edit-notes-btn" title="修改备注"><i class="fas fa-pencil-alt"></i></button>
                <?php if($license_item['status'] === 'active' || $license_item['status'] === 'expired'): ?>
                <button class="btn btn-sm btn-primary add-time-btn" title="增加时间"><i class="fas fa-plus-circle"></i></button>
                <a href="actions/update_license.php?id=<?= $license_item['id'] ?>&action=unbind" class="btn btn-sm btn-warning" onclick="return confirm('确定要解绑该设备吗？解绑后用户可重新激活。');" title="解绑设备"><i class="fas fa-unlink"></i></a>
                <?php endif; ?>
                <?php if ($license_item['status'] === 'disabled'): ?>
                    <a href="actions/update_status.php?id=<?= $license_item['id'] ?>&action=enable" class="btn btn-sm btn-success" title="启用"><i class="fas fa-check"></i></a>
                <?php else: ?>
                    <a href="actions/update_status.php?id=<?= $license_item['id'] ?>&action=disable" class="btn btn-sm btn-warning" title="禁用"><i class="fas fa-times"></i></a>
                <?php endif; ?>
                <a href="actions/update_status.php?id=<?= $license_item['id'] ?>&action=delete" class="btn btn-sm btn-danger single-delete-btn" title="删除"><i class="fas fa-trash"></i></a>
            </div>
        </td>
    </tr>
    <?php endforeach; ?>
</tbody>
            </table>
        </div>
    </div>
    </div></section>
</div>
<!-- (模态框和footer保持不变) -->
<textarea id="copyBuffer" style="position: absolute; left: -9999px;"></textarea>

<!-- 增加时间模态框 -->
<div class="modal fade" id="addTimeModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-plus-circle"></i> 增加时间</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form action="actions/update_license.php" method="post">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add_time">
                    <input type="hidden" name="id" id="modalLicenseId">

                    <div class="form-group">
                        <label>激活码：</label>
                        <span id="modalLicenseKey" class="font-weight-bold text-primary"></span>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="add_value">增加数量：</label>
                                <input type="number" name="add_value" id="add_value" class="form-control" min="1" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="add_unit">时间单位：</label>
                                <select name="add_unit" id="add_unit" class="form-control" required>
                                    <option value="hour">小时</option>
                                    <option value="day" selected>天</option>
                                    <option value="month">月</option>
                                    <option value="year">年</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">确认增加</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 生成成功弹窗 -->
<div class="modal fade" id="generateSuccessModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title"><i class="fas fa-check-circle"></i> 激活码生成成功</h5>
                <button type="button" class="close text-white" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    成功生成 <span id="generatedCount">0</span> 个激活码，套餐：<span id="generatedPlan"></span>
                </div>

                <div class="form-group">
                    <label>生成的激活码：</label>
                    <textarea id="generatedKeysText" class="form-control" rows="10" readonly></textarea>
                </div>

                <div class="text-center">
                    <button type="button" class="btn btn-primary" id="copyGeneratedKeys">
                        <i class="fas fa-copy"></i> 复制所有激活码
                    </button>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-success" data-dismiss="modal" onclick="location.reload()">
                    <i class="fas fa-refresh"></i> 刷新页面
                </button>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>