<?php
// 包含数据库连接文件
require_once '../includes/db.php';

// ★★★ 1. 统一设置PHP时区 (非常重要) ★★★
date_default_timezone_set('Asia/Shanghai');

/**
 * 日志记录函数 (写入数据库)
 * @param PDO $pdo 数据库连接对象
 * @param string|null $key 许可证密钥
 * @param string $uid 设备唯一ID
 * @param string $type 请求类型
 * @param string $ip IP地址
 * @param string $status 操作结果状态
 */
function write_log($pdo, $key, $uid, $type, $ip, $status) {
    try {
        // 使用您原来的数据库日志记录逻辑
        $stmt = $pdo->prepare("INSERT INTO logs (license_key, device_uid, log_type, ip_address, status) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute([$key, $uid, $type, $ip, $status]);
    } catch (PDOException $e) {
        // 如果日志记录失败，不要中断主流程，但可以在服务器错误日志中记录下来
        error_log("Failed to write to database log: " . $e->getMessage());
    }
}

// --- 请求参数处理 ---
$uid = $_GET['uid'] ?? '';
$uks = $_GET['uks'] ?? ''; // 用户激活码
$type = $_GET['type'] ?? 'activate'; // 默认是激活请求
$ip = $_SERVER['REMOTE_ADDR'];
$skeys_client = $_GET['skeys'] ?? '';

// 必须提供UID
if (empty($uid)) {
    // 注意：因为没有$pdo对象，所以这里无法记录日志，但这是小概率边缘情况
    exit('INVALID_PARAMS');
}

// ===================================================================
// 场景一：用户使用激活码进行激活
// ===================================================================
if ($type === 'activate' && !empty($uks)) {
    $stmt = $pdo->prepare("SELECT l.*, p.validity_value, p.validity_unit FROM licenses l JOIN plans p ON l.plan_id = p.id WHERE l.license_key = ?");
    $stmt->execute([$uks]);
    $license = $stmt->fetch();

    if (!$license) {
        write_log($pdo, $uks, $uid, 'activate', $ip, 'FAILED_KEY_NOT_FOUND');
        exit('KEY_NOT_FOUND');
    }
    if ($license['status'] === 'disabled') {
        write_log($pdo, $uks, $uid, 'activate', $ip, 'FAILED_KEY_DISABLED');
        exit('KEY_DISABLED');
    }
    
    $expires_at = null;

    if ($license['status'] === 'inactive') {
        $now_php = new DateTime();
        // ★★★ 2. 由PHP计算所有时间，不再依赖MySQL的NOW() ★★★
        $expires_at = (new DateTime())->modify('+' . $license['validity_value'] . ' ' . $license['validity_unit']);
        
        $update_stmt = $pdo->prepare("UPDATE licenses SET device_uid = ?, status = 'active', activated_at = ?, expires_at = ?, last_ip = ? WHERE id = ?");
        $update_stmt->execute([$uid, $now_php->format('Y-m-d H:i:s'), $expires_at->format('Y-m-d H:i:s'), $ip, $license['id']]);

    } elseif ($license['status'] === 'active' || $license['status'] === 'expired') {
        // 如果是已激活用户在同一设备上再次操作
        if ($license['device_uid'] !== $uid) {
            write_log($pdo, $uks, $uid, 'activate', $ip, 'FAILED_KEY_BOUND_OTHER');
            exit('KEY_BOUND_OTHER');
        }
        $expires_at = new DateTime($license['expires_at']);
        if (new DateTime() > $expires_at) {
            if ($license['status'] !== 'expired') { $pdo->prepare("UPDATE licenses SET status = 'expired' WHERE id = ?")->execute([$license['id']]); }
            write_log($pdo, $uks, $uid, 'activate', $ip, 'FAILED_KEY_EXPIRED');
            exit('KEY_EXPIRED');
        }
    } else {
        write_log($pdo, $uks, $uid, 'activate', $ip, 'FAILED_INVALID_STATUS');
        exit('INVALID_STATUS');
    }
    
    // --- 生成返回给客户端的 Skeys ---
    $d = (int)($expires_at->getTimestamp() * 1000);
    $part2 = ($d + 10000) * 903 / 100000;
    $part1 = md5($uid . $d);
    $prstr = "ppmt"; $prnum = "1200";
    $hash1 = md5($prstr . "100000000:00" . $part1 . $prstr . "100000000:00");
    $part3 = md5($hash1 . $prstr . "100000000:00" . $part2 . md5($uid) . $prnum);
    $skeys = $part1 . $part2 . $part3;
    
    write_log($pdo, $uks, $uid, 'activate', $ip, 'SUCCESS');
    echo $skeys;
    exit();
}

// ===================================================================
// 场景二：App启动或操作时进行状态检查
// ===================================================================
if ($type === 'verify' && !empty($skeys_client)) {
    // ★★★ 核心修正：增加 ORDER BY activated_at DESC LIMIT 1 ★★★
    $stmt = $pdo->prepare("SELECT * FROM licenses WHERE device_uid = ? ORDER BY activated_at DESC LIMIT 1");
    $stmt->execute([$uid]);
    $license = $stmt->fetch();

    if (!$license) {
        write_log($pdo, null, $uid, 'verify', $ip, 'FAILED_DEVICE_NOT_FOUND');
        exit('DEVICE_NOT_FOUND');
    }
    if ($license['status'] === 'disabled') {
        write_log($pdo, $license['license_key'], $uid, 'verify', $ip, 'FAILED_KEY_DISABLED');
        exit('KEY_DISABLED');
    }
    
    $expires_at_from_db = new DateTime($license['expires_at']);
    
    if (new DateTime() > $expires_at_from_db) {
        if ($license['status'] !== 'expired') {
             $pdo->prepare("UPDATE licenses SET status = 'expired' WHERE id = ?")->execute([$license['id']]);
        }
        write_log($pdo, $license['license_key'], $uid, 'verify', $ip, 'FAILED_KEY_EXPIRED');
        exit('KEY_EXPIRED');
    }

    // ★★★ 3. 安全增强：在服务器端重新生成skeys并与客户端传来的进行比对 ★★★
    $d = (int)($expires_at_from_db->getTimestamp() * 1000);
    $part2 = ($d + 10000) * 903 / 100000;
    $part1 = md5($uid . $d);
    $prstr = "ppmt"; $prnum = "1200";
    $hash1 = md5($prstr . "100000000:00" . $part1 . $prstr . "100000000:00");
    $part3 = md5($hash1 . $prstr . "100000000:00" . $part2 . md5($uid) . $prnum);
    $skeys_server = $part1 . $part2 . $part3;

    if ($skeys_client !== $skeys_server) {
        write_log($pdo, $license['license_key'], $uid, 'verify', $ip, 'FAILED_INVALID_SKEY');
        exit('INVALID_SKEY');
    }

    // 所有检查通过
    write_log($pdo, $license['license_key'], $uid, 'verify', $ip, 'SUCCESS');
    echo 'VALID';
    exit();
}

// 如果请求不满足任何场景，则为无效请求
write_log($pdo, $uks, $uid, $type, $ip, 'FAILED_INVALID_REQUEST');
exit('INVALID_REQUEST');
?>