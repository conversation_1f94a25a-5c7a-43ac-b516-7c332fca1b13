<?php
// 包含数据库连接文件
require_once '../includes/db.php';

// ★★★ 1. 统一设置PHP时区 (非常重要) ★★★
date_default_timezone_set('Asia/Shanghai');

/**
 * 日志记录函数 (写入数据库)
 * @param PDO $pdo 数据库连接对象
 * @param string|null $key 许可证密钥
 * @param string $uid 设备唯一ID
 * @param string $type 请求类型
 * @param string $ip IP地址
 * @param string $status 操作结果状态
 */
function write_log($pdo, $key, $uid, $type, $ip, $status) {
    try {
        // 使用您原来的数据库日志记录逻辑
        $stmt = $pdo->prepare("INSERT INTO logs (license_key, device_uid, log_type, ip_address, status) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute([$key, $uid, $type, $ip, $status]);
    } catch (PDOException $e) {
        // 如果日志记录失败，不要中断主流程，但可以在服务器错误日志中记录下来
        error_log("Failed to write to database log: " . $e->getMessage());
    }
}

// --- 请求参数处理 ---
$uid = $_GET['uid'] ?? '';
$uks = $_GET['uks'] ?? ''; // 用户激活码
$type = $_GET['type'] ?? 'activate'; // 默认是激活请求
$ip = $_SERVER['REMOTE_ADDR'];
$skeys_client = $_GET['skeys'] ?? '';

// 必须提供UID
if (empty($uid)) {
    // 注意：因为没有$pdo对象，所以这里无法记录日志，但这是小概率边缘情况
    exit('INVALID_PARAMS');
}

// ===================================================================
// 场景一：用户使用激活码进行激活（支持多设备）
// ===================================================================
if ($type === 'activate' && !empty($uks)) {
    // 获取激活码和套餐信息
    $stmt = $pdo->prepare("SELECT l.*, p.validity_value, p.validity_unit FROM licenses l JOIN plans p ON l.plan_id = p.id WHERE l.license_key = ?");
    $stmt->execute([$uks]);
    $license = $stmt->fetch();

    if (!$license) {
        write_log($pdo, $uks, $uid, 'activate', $ip, 'FAILED_KEY_NOT_FOUND');
        exit('KEY_NOT_FOUND');
    }
    if ($license['status'] === 'disabled') {
        write_log($pdo, $uks, $uid, 'activate', $ip, 'FAILED_KEY_DISABLED');
        exit('KEY_DISABLED');
    }

    // 检查该设备是否已经绑定到此激活码
    $device_stmt = $pdo->prepare("SELECT * FROM license_devices WHERE license_id = ? AND device_uid = ? AND status = 'active'");
    $device_stmt->execute([$license['id'], $uid]);
    $existing_device = $device_stmt->fetch();

    $expires_at = null;
    $now_php = new DateTime();

    if ($license['status'] === 'inactive') {
        // 首次激活
        $expires_at = (new DateTime())->modify('+' . $license['validity_value'] . ' ' . $license['validity_unit']);

        // 更新激活码状态
        $update_stmt = $pdo->prepare("UPDATE licenses SET status = 'active', activated_at = ?, expires_at = ?, last_ip = ?, bound_devices = 1 WHERE id = ?");
        $update_stmt->execute([$now_php->format('Y-m-d H:i:s'), $expires_at->format('Y-m-d H:i:s'), $ip, $license['id']]);

        // 添加设备绑定记录
        $device_insert = $pdo->prepare("INSERT INTO license_devices (license_id, device_uid, activated_at, last_ip, verify_count) VALUES (?, ?, ?, ?, 1)");
        $device_insert->execute([$license['id'], $uid, $now_php->format('Y-m-d H:i:s'), $ip]);

    } elseif ($license['status'] === 'active' || $license['status'] === 'expired') {
        $expires_at = new DateTime($license['expires_at']);

        if ($existing_device) {
            // 设备已绑定，检查是否过期
            if (new DateTime() > $expires_at) {
                if ($license['status'] !== 'expired') {
                    $pdo->prepare("UPDATE licenses SET status = 'expired' WHERE id = ?")->execute([$license['id']]);
                }
                write_log($pdo, $uks, $uid, 'activate', $ip, 'FAILED_KEY_EXPIRED');
                exit('KEY_EXPIRED');
            }

            // 更新设备最后验证时间
            $pdo->prepare("UPDATE license_devices SET last_verify_at = ?, last_ip = ?, verify_count = verify_count + 1 WHERE id = ?")
                ->execute([$now_php->format('Y-m-d H:i:s'), $ip, $existing_device['id']]);

        } else {
            // 新设备要绑定，检查设备数量限制
            $current_devices = $pdo->prepare("SELECT COUNT(*) FROM license_devices WHERE license_id = ? AND status = 'active'");
            $current_devices->execute([$license['id']]);
            $device_count = $current_devices->fetchColumn();

            if ($device_count >= $license['max_devices']) {
                write_log($pdo, $uks, $uid, 'activate', $ip, 'FAILED_MAX_DEVICES_REACHED');
                exit('MAX_DEVICES_REACHED');
            }

            // 检查是否过期
            if (new DateTime() > $expires_at) {
                if ($license['status'] !== 'expired') {
                    $pdo->prepare("UPDATE licenses SET status = 'expired' WHERE id = ?")->execute([$license['id']]);
                }
                write_log($pdo, $uks, $uid, 'activate', $ip, 'FAILED_KEY_EXPIRED');
                exit('KEY_EXPIRED');
            }

            // 添加新设备绑定
            $device_insert = $pdo->prepare("INSERT INTO license_devices (license_id, device_uid, activated_at, last_ip, verify_count) VALUES (?, ?, ?, ?, 1)");
            $device_insert->execute([$license['id'], $uid, $now_php->format('Y-m-d H:i:s'), $ip]);

            // 更新激活码的绑定设备数
            $pdo->prepare("UPDATE licenses SET bound_devices = bound_devices + 1 WHERE id = ?")->execute([$license['id']]);
        }
    } else {
        write_log($pdo, $uks, $uid, 'activate', $ip, 'FAILED_INVALID_STATUS');
        exit('INVALID_STATUS');
    }

    // --- 生成返回给客户端的 Skeys ---
    $d = (int)($expires_at->getTimestamp() * 1000);
    $part2 = ($d + 10000) * 903 / 100000;
    $part1 = md5($uid . $d);
    $prstr = "ppmt"; $prnum = "1200";
    $hash1 = md5($prstr . "100000000:00" . $part1 . $prstr . "100000000:00");
    $part3 = md5($hash1 . $prstr . "100000000:00" . $part2 . md5($uid) . $prnum);
    $skeys = $part1 . $part2 . $part3;

    write_log($pdo, $uks, $uid, 'activate', $ip, 'SUCCESS');
    echo $skeys;
    exit();
}

// ===================================================================
// 场景二：App启动或操作时进行状态检查（支持多设备）
// ===================================================================
if ($type === 'verify' && !empty($skeys_client)) {
    // 查找该设备绑定的激活码
    $stmt = $pdo->prepare("
        SELECT l.*, ld.last_verify_at, ld.verify_count, ld.id as device_id
        FROM license_devices ld
        JOIN licenses l ON ld.license_id = l.id
        WHERE ld.device_uid = ? AND ld.status = 'active' AND l.status IN ('active', 'expired')
        ORDER BY ld.activated_at DESC LIMIT 1
    ");
    $stmt->execute([$uid]);
    $device_license = $stmt->fetch();

    if (!$device_license) {
        write_log($pdo, null, $uid, 'verify', $ip, 'FAILED_DEVICE_NOT_FOUND');
        exit('DEVICE_NOT_FOUND');
    }
    if ($device_license['status'] === 'disabled') {
        write_log($pdo, $device_license['license_key'], $uid, 'verify', $ip, 'FAILED_KEY_DISABLED');
        exit('KEY_DISABLED');
    }

    $expires_at_from_db = new DateTime($device_license['expires_at']);

    if (new DateTime() > $expires_at_from_db) {
        if ($device_license['status'] !== 'expired') {
             $pdo->prepare("UPDATE licenses SET status = 'expired' WHERE id = ?")->execute([$device_license['id']]);
        }
        write_log($pdo, $device_license['license_key'], $uid, 'verify', $ip, 'FAILED_KEY_EXPIRED');
        exit('KEY_EXPIRED');
    }

    // ★★★ 3. 安全增强：在服务器端重新生成skeys并与客户端传来的进行比对 ★★★
    $d = (int)($expires_at_from_db->getTimestamp() * 1000);
    $part2 = ($d + 10000) * 903 / 100000;
    $part1 = md5($uid . $d);
    $prstr = "ppmt"; $prnum = "1200";
    $hash1 = md5($prstr . "100000000:00" . $part1 . $prstr . "100000000:00");
    $part3 = md5($hash1 . $prstr . "100000000:00" . $part2 . md5($uid) . $prnum);
    $skeys_server = $part1 . $part2 . $part3;

    if ($skeys_client !== $skeys_server) {
        write_log($pdo, $device_license['license_key'], $uid, 'verify', $ip, 'FAILED_INVALID_SKEY');
        exit('INVALID_SKEY');
    }

    // 更新设备验证记录
    $now_php = new DateTime();
    $pdo->prepare("UPDATE license_devices SET last_verify_at = ?, last_ip = ?, verify_count = verify_count + 1 WHERE id = ?")
        ->execute([$now_php->format('Y-m-d H:i:s'), $ip, $device_license['device_id']]);

    // 所有检查通过
    write_log($pdo, $device_license['license_key'], $uid, 'verify', $ip, 'SUCCESS');
    echo 'VALID';
    exit();
}

// 如果请求不满足任何场景，则为无效请求
write_log($pdo, $uks, $uid, $type, $ip, 'FAILED_INVALID_REQUEST');
exit('INVALID_REQUEST');
?>