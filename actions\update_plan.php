<?php
require_once '../includes/check_login.php';
require_once '../includes/db.php';

// --- 处理 POST 请求 (添加/编辑) ---
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    // 公共参数
    $plan_name = trim($_POST['plan_name'] ?? '');
    $validity_value = (int)($_POST['validity_value'] ?? 0);
    $validity_unit = $_POST['validity_unit'] ?? '';
    $notes = trim($_POST['notes'] ?? '');
    $is_active = isset($_POST['is_active']) ? 1 : 0;

    // 参数校验
    if (empty($plan_name) || $validity_value <= 0 || !in_array($validity_unit, ['hour', 'day', 'month', 'year'])) {
        die('参数错误，请检查输入！');
    }

    // 添加套餐
    if ($action === 'add') {
        $stmt = $pdo->prepare("INSERT INTO plans (plan_name, validity_value, validity_unit, notes, is_active) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute([$plan_name, $validity_value, $validity_unit, $notes, $is_active]);
    }
    
    // 编辑套餐
    if ($action === 'edit') {
        $id = (int)($_POST['id'] ?? 0);
        if ($id > 0) {
            $stmt = $pdo->prepare("UPDATE plans SET plan_name = ?, validity_value = ?, validity_unit = ?, notes = ?, is_active = ? WHERE id = ?");
            $stmt->execute([$plan_name, $validity_value, $validity_unit, $notes, $is_active, $id]);
        }
    }
}

// --- 处理 GET 请求 (删除) ---
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    $action = $_GET['action'] ?? '';
    $id = (int)($_GET['id'] ?? 0);

    // 删除套餐
    if ($action === 'delete' && $id > 0) {
        try {
            // 先检查是否有激活码关联此套餐，有则不允许删除
            $stmt_check = $pdo->prepare("SELECT COUNT(*) FROM licenses WHERE plan_id = ?");
            $stmt_check->execute([$id]);
            if ($stmt_check->fetchColumn() > 0) {
                // 你可以在这里设置一个session消息来提示用户
                $_SESSION['flash_message'] = "无法删除套餐，因为已有激活码正在使用此套餐。";
            } else {
                $stmt = $pdo->prepare("DELETE FROM plans WHERE id = ?");
                $stmt->execute([$id]);
            }
        } catch (PDOException $e) {
            // 处理外键约束等数据库错误
            $_SESSION['flash_message'] = "删除失败，可能存在关联数据。错误: " . $e->getCode();
        }
    }
}

header('Location: ../plans.php');
exit();