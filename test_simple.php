<!DOCTYPE html>
<html>
<head>
    <title>简单功能测试</title>
    <script src="assets/plugins/jquery/jquery.min.js"></script>
    <link rel="stylesheet" href="assets/plugins/fontawesome-free/css/all.min.css">
    <link rel="stylesheet" href="assets/plugins/admin-lte/css/adminlte.min.css">
</head>
<body>
    <div class="container mt-4">
        <h2>功能测试页面</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3>解绑设备测试</h3>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label>激活码ID (License ID):</label>
                            <input type="number" id="deviceId" value="530" class="form-control">
                            <small class="text-muted">注意：这里输入的是激活码的ID，不是设备UID</small>
                        </div>
                        <button class="btn btn-danger" onclick="testUnbind()">
                            <i class="fas fa-times"></i> 测试解绑
                        </button>
                        <div id="unbindResult" class="mt-3"></div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3>Skeys查看测试</h3>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label>激活码:</label>
                            <input type="text" id="licenseKey" value="TP-C32FD25FA8073C20D7B7167313E8BA9A" class="form-control">
                        </div>
                        <div class="form-group">
                            <label>设备UID:</label>
                            <input type="text" id="deviceUid" value="TEST_3FP0HC764_MD1FX2J1" class="form-control">
                        </div>
                        <button class="btn btn-info" onclick="testSkeys()">
                            <i class="fas fa-key"></i> 测试Skeys
                        </button>
                        <div id="skeysResult" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h3>图标测试</h3>
                    </div>
                    <div class="card-body">
                        <p>测试各种图标是否正常显示：</p>
                        <button class="btn btn-xs btn-outline-danger" style="padding: 1px 4px; font-size: 10px;">
                            <i class="fas fa-times"></i>
                        </button>
                        <button class="btn btn-sm btn-info ml-2">
                            <i class="fas fa-key"></i> Skeys
                        </button>
                        <button class="btn btn-sm btn-success ml-2">
                            <i class="fas fa-check"></i> 成功
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
    function testUnbind() {
        var deviceId = $('#deviceId').val();
        $('#unbindResult').html('<div class="alert alert-info">正在测试...</div>');
        
        $.post('actions/update_license.php', {
            action: 'unbind_device',
            device_id: deviceId
        }).done(function(response) {
            console.log('解绑响应:', response);
            
            try {
                var result = typeof response === 'string' ? JSON.parse(response) : response;
                if (result.success) {
                    $('#unbindResult').html('<div class="alert alert-success">' + result.message + '</div>');
                } else {
                    $('#unbindResult').html('<div class="alert alert-danger">失败：' + result.message + '</div>');
                }
            } catch (e) {
                $('#unbindResult').html('<div class="alert alert-danger">响应解析失败：' + e.message + '</div>');
                console.error('原始响应:', response);
            }
        }).fail(function(xhr, status, error) {
            $('#unbindResult').html('<div class="alert alert-danger">请求失败：' + error + '</div>');
            console.error('失败详情:', xhr.responseText);
        });
    }
    
    function testSkeys() {
        var licenseKey = $('#licenseKey').val();
        var deviceUid = $('#deviceUid').val();
        $('#skeysResult').html('<div class="alert alert-info">正在获取...</div>');
        
        $.post('actions/get_skeys.php', {
            license_key: licenseKey,
            device_uid: deviceUid
        }).done(function(response) {
            console.log('Skeys响应:', response);
            
            try {
                var result = typeof response === 'string' ? JSON.parse(response) : response;
                if (result.success) {
                    $('#skeysResult').html('<div class="alert alert-success">Skeys: <br><textarea class="form-control mt-2" rows="3">' + result.skeys + '</textarea></div>');
                } else {
                    $('#skeysResult').html('<div class="alert alert-danger">失败：' + result.error + '</div>');
                }
            } catch (e) {
                $('#skeysResult').html('<div class="alert alert-danger">响应解析失败：' + e.message + '</div>');
                console.error('原始响应:', response);
            }
        }).fail(function(xhr, status, error) {
            $('#skeysResult').html('<div class="alert alert-danger">请求失败：' + error + '</div>');
            console.error('失败详情:', xhr.responseText);
        });
    }
    </script>
</body>
</html>
