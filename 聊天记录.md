
1.第一个对话。
现在来解决下面的问题。先出方案 然后在进行解决。
问题1.现在界面中增加时间按钮点击没用。修改备注也不能用。
问题2.生成卡密之后先弹出来一个对话框，里面有所有的卡密信息，内容和批量复制一样，就等于有时候生成就是给指定的人的，就不用在进行手动找到复制了，因为后面卡密肯定很多。
需要扩展的问题
1. 如果我想要一个激活码可以激活多个设备，多个设备的话，是不同的uid 激活码可以用的设备数我可以进行设置自定义。这种情况应该怎么解决，怎么执行这一些列的操作以及判断。
我需要有一个更加详细完善的仪表盘，展示更多信息，比如当天登录多少个设备，验证过多少次信息。每个设备访问过多少次，当天激活了多少个验证码，新增了多少台设备等等。
然后因为我服务器已经运行了这个代码， 目前这个代码是我拷贝下来的。没有设备进行测试，所以我还需要你开发一个测试设备发送请求，以及绑定的界面。用于本地的设备绑定验证测试。
我目前服务器是2h2g的配置，阿里云服务器，能够承受多少的并发。如果有多少个设备用户不断访问我需要扩展配置。

激活码列表的筛选我需要加到表格里面，意思就是直接点击表格里面的套餐，这个不是进行排序，而是进行套餐的选择。

操作日志的话，如果数据太多会不会卡， 数据多的话， 多少算多。
操作日志表单里面结果显示中文，现在英文看着不方便。
查询日志也要根据跟多的字段来进行查询。比如ip地址，然后类型也可以进行筛选，结果等都需要进行筛选，筛选就直接用点击表格的表头进行筛选，能筛选的筛选，不能筛选的就排序。
