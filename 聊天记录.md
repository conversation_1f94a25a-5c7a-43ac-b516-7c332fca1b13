
1.第一个对话。
现在来解决下面的问题。先出方案 然后在进行解决。
问题1.现在界面中增加时间按钮点击没用。修改备注也不能用。
问题2.生成卡密之后先弹出来一个对话框，里面有所有的卡密信息，内容和批量复制一样，就等于有时候生成就是给指定的人的，就不用在进行手动找到复制了，因为后面卡密肯定很多。
需要扩展的问题
1. 如果我想要一个激活码可以激活多个设备，多个设备的话，是不同的uid 激活码可以用的设备数我可以进行设置自定义。这种情况应该怎么解决，怎么执行这一些列的操作以及判断。
我需要有一个更加详细完善的仪表盘，展示更多信息，比如当天登录多少个设备，验证过多少次信息。每个设备访问过多少次，当天激活了多少个验证码，新增了多少台设备等等。
然后因为我服务器已经运行了这个代码， 目前这个代码是我拷贝下来的。没有设备进行测试，所以我还需要你开发一个测试设备发送请求，以及绑定的界面。用于本地的设备绑定验证测试。
我目前服务器是2h2g的配置，阿里云服务器，能够承受多少的并发。如果有多少个设备用户不断访问我需要扩展配置。

激活码列表的筛选我需要加到表格里面，意思就是直接点击表格里面的套餐，这个不是进行排序，而是进行套餐的选择。

操作日志的话，如果数据太多会不会卡， 数据多的话， 多少算多。
操作日志表单里面结果显示中文，现在英文看着不方便。
查询日志也要根据跟多的字段来进行查询。比如ip地址，然后类型也可以进行筛选，结果等都需要进行筛选，筛选就直接用点击表格的表头进行筛选，能筛选的筛选，不能筛选的就排序。




第二次对话
1.激活码列表里面点击激活码可以直接复制。
2.激活码列表里面筛选套餐和筛选状态就不要进行点击排序很麻烦。 并且我刚刚点击了筛选10设备年卡，我数据库里面只有一个，按理说应该是只出现一个，他现在出现了很多个说明逻辑还是有问题错误的。包括状态都筛选也不对。后台不要任何英文，都要改成中文。激活码列表最大可显示的数据应该是10/30/50/100/500/1000
3.检查激活码里面的逻辑，等于他里面有两个框 看着很不美观 要解决这个问题。应该是ui样式的问题，我昨天新增加了一个表样式就成这样了，没加的话功能有的不能实现。
4.我想要的多设备不是套餐，而是每一个激活码都可以增加多个设备，是这个意思，比如我生成的时候就会有输入，默认是一个设备，如果需要多个设备话就直接写对应的设备数套餐只是时间。并且已经生成的验证码也可以增加设备。
5.仪表盘里面，下面的总绑定设备数，和已经绑定的激活码，和已过期的激活码，已过期不需要展示，已绑定的和上面已激活是不是一个意思，总绑定的设备数，应该是uid 的数量，这些不用另起一行，直接放到第一行就行，样式和今日统计一样，那个大小就行，现在这个太占地方了。
6.设备测试不起作用，我输入了激活码提示成功，但是我看那个对应的激活码也没有显示激活，测试验证也会失败。
7.日志里面所有的筛选能不能热更新，就是输入框还没有点击确定，根据现有的数据就筛选，比如我输入1就出现所有1的数据。14就是14的数据 147就是147的数据。然后所有的筛选都不起作用。我测试过之后。逻辑其实和激活码列表里面的search一样， 后台不要任何英文，都要改成中文。



第三次对话
问题1.激活码列表里面绑定设备用列表来展示，别用滚动条，套餐里面最大5设备，改成5设备，节省宽度，激活码状态目前是一个设备的激活之后他就会提示已激活，但是比如5个设备 只有激活满5次才会提示已激活，应该显示进度的，比如激活3/5  然后如果多设备的话，应该在绑定设备的表格里面有一个解绑，就可以解绑指定的设备，不需要在操作里面在单独添加一个按钮，那个针对一个还行 多个不行。表格里面内容尽可能的节省空间宽度。

问题2.操作日志里面复杂了，还是恢复以前的样子吧。现在功能都不行。


问题3.仪表盘已绑定码和已激活重复，就删除已绑定码，

问题4.每次切换菜单都会刷新界面，php是都会这样吗，如果是的话，那就不用修改，如果有解决方式的话，你先提出来，我确定了 在进行修改。利弊说出来。

问题5.激活码列表里面套餐和状态都恢复以前的排序吧，然后按照以前的方式，进行筛选，之前是只筛选套餐，现在是增加一个筛选激活状态，
