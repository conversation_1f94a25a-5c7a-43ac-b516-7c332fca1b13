// 【【【 最终的、正确的、实现了方案B的 main.js 】】】

$(function () {
  // ===================================================================
  // 1. 初始化 DataTables，并正确启用 Select 扩展
  // ===================================================================
  var table = $('#licensesTable').DataTable({
    "paging": true,
    "lengthChange": true,
    "searching": true,
    "ordering": true,
    "info": true,
    "autoWidth": false,
    "responsive": true,
    "order": [[ 1, "desc" ]],
    "columnDefs": [ 
        { "orderable": false, "targets": 'no-sort' },
        // ★★★ 关键：将第一列定义为复选框选择列 ★★★
        { "targets": 0, "className": 'select-checkbox' }
    ],
    "language": {
        "url": "assets/json/zh-Hans.json"
    },
    // ★★★ 关键：启用 Select 功能 ★★★
    "select": {
        "style": 'multi',      // 允许多选
        "selector": 'td:first-child' // 点击第一列的单元格即可触发行选中
    }
  });

  // ===================================================================
  // 2. 全选逻辑 (方案B：只选当前页)
  // ===================================================================
  $('#selectAll').on('click', function(){
     if(this.checked){
        // table.rows({ page: 'current' }) 获取当前页的所有行
        table.rows({ page: 'current' }).select();
     } else {
        table.rows({ page: 'current' }).deselect();
     }
  });

  // 监听DataTables的选择事件，来同步“全选”复选框的状态
  table.on('select deselect', function () {
      var selectedRows = table.rows({ selected: true }).count();
      var pageRows = table.rows({ page: 'current' }).count();
      var selectAllCheckbox = $('#selectAll').get(0);
      
      if (selectedRows === pageRows && pageRows > 0) {
          selectAllCheckbox.checked = true;
          selectAllCheckbox.indeterminate = false;
      } else if (selectedRows > 0) {
          selectAllCheckbox.checked = false;
          selectAllCheckbox.indeterminate = true;
      } else {
          selectAllCheckbox.checked = false;
          selectAllCheckbox.indeterminate = false;
      }
  });

  // 翻页时，取消所有页面的“全选”状态
  table.on('page.dt', function(){
      $('#selectAll').get(0).checked = false;
      $('#selectAll').get(0).indeterminate = false;
  });

  // ===================================================================
  // 3. 批量操作按钮逻辑
  // ===================================================================

  // 批量删除按钮
  $('#deleteSelectedBtn').on('click', function() {
      // ★★★ 使用正确的API获取选中的行 ★★★
      var selectedRows = table.rows({ selected: true });
      if (selectedRows.count() === 0) {
          alert('请至少选择一个激活码！');
          return;
      }
      if (confirm('确定要删除选中的 ' + selectedRows.count() + ' 个激活码吗？此操作不可恢复！')) {
          var form = $('<form>', { 'action': 'actions/update_status.php', 'method': 'post', 'style': 'display:none;' });
          form.append($('<input>', { name: 'action', value: 'delete', type: 'hidden' }));
          
          // ★★★ 从数据源获取ID，而不是DOM ★★★
          selectedRows.data().each(function(rowData) {
              // rowData[1] 是ID列
              form.append($('<input>', { name: 'ids[]', value: rowData[1], type: 'hidden' }));
          });
          $('body').append(form);
          form.submit();
      }
  });
  
  // 批量复制按钮 (同样使用新API)
  $("#copySelectedBtn").on("click", function () {
    var selectedRows = table.rows({ selected: true });
    if (selectedRows.count() === 0) {
        alert("请先选择要复制的激活码！");
        return;
    }
    var selectedKeys = [];
    selectedRows.data().each(function(rowData) {
        // rowData[2] 是激活码列, rowData[3] 是套餐列
        selectedKeys.push(rowData[3] + '：' + rowData[2]);
    });
    var textToCopy = selectedKeys.join("\n");
    $('#copyBuffer').val(textToCopy).select();
    try {
        document.execCommand("copy");
        alert("已复制 " + selectedKeys.length + " 条记录到剪贴板！");
    } catch (err) {
        alert('复制失败，可能是浏览器不支持。');
    }
  });

  // ===================================================================
  // 4. 行内操作与调试按钮 (调试按钮现在可以移除了，但我暂时保留)
  // ===================================================================
  
  // 调试按钮
  $('#debugDeleteBtn').on('click', function() {
      var selectedRows = table.rows({ selected: true });
      var collectedIds = [];
      selectedRows.data().each(function(rowData) {
          collectedIds.push(rowData[1]);
      });
      alert("【调试】选中行数: " + selectedRows.count() + "\n选中ID: " + collectedIds.join(', '));
  });

  // (其他行内操作逻辑保持不变)
  $('#licensesTable tbody').on('click', '.edit-notes-btn', function() { /* ... */ });
  $('#licensesTable tbody').on('keypress blur', '.notes-input', function(e) { /* ... */ });
  $('#licensesTable tbody').on('click', '.add-time-btn', function() { /* ... */ });
  $('#licensesTable tbody').on('click', 'a.single-delete-btn, a[title="禁用"], a[title="启用"]', function(e) { /* ... */ });

});


// 【【【 在 main.js 文件末尾追加这段新代码 】】】

/*********************************************************************
 * 
 *                     套餐管理页面 (plans.php) 的交互逻辑
 * 
 *********************************************************************/
$(function() {
    // 检查当前页面是否是 plans.php，如果是才执行下面的代码
    // 这样可以避免在 index.php 页面上执行不必要的JS
    if ($('body').find('#planFormCard').length > 0) {

        // 点击“编辑”按钮，将该行数据填充到右侧的表单中
        $('.edit-plan-btn').on('click', function() {
            var button = $(this);
            
            // 改变表单标题和按钮文字，进入“编辑模式”
            $('#planFormTitle').html('<i class="fas fa-edit"></i> 编辑套餐');
            $('#planSubmitBtn').text('确认修改');
            $('#planAction').val('edit');

            // 从按钮的 data-* 属性中获取数据并填充到表单
            $('#planId').val(button.data('id'));
            $('#plan_name').val(button.data('name'));
            $('#validity_value').val(button.data('value'));
            $('#validity_unit').val(button.data('unit'));
            $('#notes').val(button.data('notes'));
            
            // 设置“是否可用”的开关状态
            // data('active') 返回的是数字 1 或 0，需要与 1 比较
            $('#is_active').prop('checked', button.data('active') == 1);

            // 显示“取消编辑”按钮
            $('#cancelEditBtn').show();
            
            // 为了更好的用户体验，平滑滚动到表单位置
            $('html, body').animate({
                scrollTop: $("#planFormCard").offset().top - 60 // 减去60是为了留出导航栏的高度
            }, 500);
        });

        // 点击“取消编辑”按钮，重置表单回到“添加模式”
        $('#cancelEditBtn').on('click', function() {
            $('#planFormTitle').html('<i class="fas fa-plus"></i> 添加新套餐');
            $('#planSubmitBtn').text('确认添加');
            $('#planAction').val('add');
            
            // 使用 reset() 方法清空表单所有输入
            $('#planForm')[0].reset();
            
            // 确保“是否可用”开关恢复默认选中状态
            $('#is_active').prop('checked', true);
            
            // 隐藏自己
            $(this).hide();
        });
    }
});