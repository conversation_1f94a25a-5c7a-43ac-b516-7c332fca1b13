// 【【【 最终的、正确的、实现了方案B的 main.js 】】】

$(function () {
  // ===================================================================
  // 1. 初始化 DataTables，并正确启用 Select 扩展
  // ===================================================================
  var table = $('#licensesTable').DataTable({
    "paging": true,
    "lengthChange": true,
    "searching": true,
    "ordering": true,
    "info": true,
    "autoWidth": false,
    "responsive": true,
    "order": [[ 1, "desc" ]],
    "lengthMenu": [[10, 30, 50, 100, 500, 1000, -1], [10, 30, 50, 100, 500, 1000, "全部"]],
    "pageLength": 50,
    "columnDefs": [
        { "orderable": false, "targets": 'no-sort' },
        { "orderable": false, "targets": 'filterable-header' }, // 禁用筛选列的排序
        // ★★★ 关键：将第一列定义为复选框选择列 ★★★
        { "targets": 0, "className": 'select-checkbox' }
    ],
    "language": {
        "url": "assets/json/zh-Hans.json",
        "lengthMenu": "显示 _MENU_ 条记录",
        "info": "显示第 _START_ 至 _END_ 项结果，共 _TOTAL_ 项",
        "infoEmpty": "显示第 0 至 0 项结果，共 0 项",
        "infoFiltered": "(由 _MAX_ 项结果过滤)",
        "search": "搜索:",
        "paginate": {
            "first": "首页",
            "last": "末页",
            "next": "下一页",
            "previous": "上一页"
        }
    },
    // ★★★ 关键：启用 Select 功能 ★★★
    "select": {
        "style": 'multi',      // 允许多选
        "selector": 'td:first-child' // 点击第一列的单元格即可触发行选中
    }
  });

  // ===================================================================
  // 2. 全选逻辑 (方案B：只选当前页)
  // ===================================================================
  $('#selectAll').on('click', function(){
     if(this.checked){
        // table.rows({ page: 'current' }) 获取当前页的所有行
        table.rows({ page: 'current' }).select();
     } else {
        table.rows({ page: 'current' }).deselect();
     }
  });

  // 监听DataTables的选择事件，来同步“全选”复选框的状态
  table.on('select deselect', function () {
      var selectedRows = table.rows({ selected: true }).count();
      var pageRows = table.rows({ page: 'current' }).count();
      var selectAllCheckbox = $('#selectAll').get(0);
      
      if (selectedRows === pageRows && pageRows > 0) {
          selectAllCheckbox.checked = true;
          selectAllCheckbox.indeterminate = false;
      } else if (selectedRows > 0) {
          selectAllCheckbox.checked = false;
          selectAllCheckbox.indeterminate = true;
      } else {
          selectAllCheckbox.checked = false;
          selectAllCheckbox.indeterminate = false;
      }
  });

  // 翻页时，取消所有页面的“全选”状态
  table.on('page.dt', function(){
      $('#selectAll').get(0).checked = false;
      $('#selectAll').get(0).indeterminate = false;
  });

  // ===================================================================
  // 3. 批量操作按钮逻辑
  // ===================================================================

  // 批量删除按钮
  $('#deleteSelectedBtn').on('click', function() {
      // ★★★ 使用正确的API获取选中的行 ★★★
      var selectedRows = table.rows({ selected: true });
      if (selectedRows.count() === 0) {
          alert('请至少选择一个激活码！');
          return;
      }
      if (confirm('确定要删除选中的 ' + selectedRows.count() + ' 个激活码吗？此操作不可恢复！')) {
          var form = $('<form>', { 'action': 'actions/update_status.php', 'method': 'post', 'style': 'display:none;' });
          form.append($('<input>', { name: 'action', value: 'delete', type: 'hidden' }));
          
          // ★★★ 从数据源获取ID，而不是DOM ★★★
          selectedRows.data().each(function(rowData) {
              // rowData[1] 是ID列
              form.append($('<input>', { name: 'ids[]', value: rowData[1], type: 'hidden' }));
          });
          $('body').append(form);
          form.submit();
      }
  });
  
  // 批量复制按钮 (同样使用新API)
  $("#copySelectedBtn").on("click", function () {
    var selectedRows = table.rows({ selected: true });
    if (selectedRows.count() === 0) {
        alert("请先选择要复制的激活码！");
        return;
    }
    var selectedKeys = [];
    selectedRows.data().each(function(rowData) {
        // rowData[2] 是激活码列, rowData[3] 是套餐列
        selectedKeys.push(rowData[3] + '：' + rowData[2]);
    });
    var textToCopy = selectedKeys.join("\n");
    $('#copyBuffer').val(textToCopy).select();
    try {
        document.execCommand("copy");
        alert("已复制 " + selectedKeys.length + " 条记录到剪贴板！");
    } catch (err) {
        alert('复制失败，可能是浏览器不支持。');
    }
  });

  // ===================================================================
  // 4. 行内操作与调试按钮 (调试按钮现在可以移除了，但我暂时保留)
  // ===================================================================
  
  // 调试按钮
  $('#debugDeleteBtn').on('click', function() {
      var selectedRows = table.rows({ selected: true });
      var collectedIds = [];
      selectedRows.data().each(function(rowData) {
          collectedIds.push(rowData[1]);
      });
      alert("【调试】选中行数: " + selectedRows.count() + "\n选中ID: " + collectedIds.join(', '));
  });

  // ===================================================================
  // 5. 行内操作逻辑 - 修改备注
  // ===================================================================
  $('#licensesTable tbody').on('click', '.edit-notes-btn', function() {
    var $btn = $(this);
    var $row = $btn.closest('tr');
    var $notesCell = $row.find('.notes-cell');
    var $notesText = $notesCell.find('.notes-text');
    var $notesInput = $notesCell.find('.notes-input');

    // 切换到编辑模式
    $notesText.hide();
    $notesInput.show().focus().select();
    $btn.removeClass('btn-secondary').addClass('btn-success').html('<i class="fas fa-check"></i>');
  });

  // 保存备注修改
  $('#licensesTable tbody').on('keypress blur', '.notes-input', function(e) {
    if (e.type === 'keypress' && e.which !== 13) return; // 只响应回车键或失焦

    var $input = $(this);
    var $row = $input.closest('tr');
    var $notesCell = $row.find('.notes-cell');
    var $notesText = $notesCell.find('.notes-text');
    var $btn = $row.find('.edit-notes-btn');
    var licenseId = $row.find('td:eq(1)').text(); // ID列
    var newNotes = $input.val();

    // 发送AJAX请求保存备注
    $.post('actions/update_license.php', {
      action: 'update_notes',
      id: licenseId,
      notes: newNotes
    }).done(function() {
      // 更新显示文本
      $notesText.text(newNotes);
      $notesCell.attr('data-original-text', newNotes);

      // 切换回显示模式
      $input.hide();
      $notesText.show();
      $btn.removeClass('btn-success').addClass('btn-secondary').html('<i class="fas fa-pencil-alt"></i>');
    }).fail(function() {
      alert('保存失败，请重试');
      $input.focus();
    });
  });

  // ===================================================================
  // 6. 行内操作逻辑 - 增加时间
  // ===================================================================
  $('#licensesTable tbody').on('click', '.add-time-btn', function() {
    var $btn = $(this);
    var $row = $btn.closest('tr');
    var licenseId = $row.find('td:eq(1)').text(); // ID列
    var licenseKey = $row.find('td:eq(2)').text(); // 激活码列

    // 填充模态框数据
    $('#addTimeModal').find('#modalLicenseId').val(licenseId);
    $('#addTimeModal').find('#modalLicenseKey').text(licenseKey);

    // 显示模态框
    $('#addTimeModal').modal('show');
  });

  // ===================================================================
  // 7. 行内操作逻辑 - 其他操作确认
  // ===================================================================
  $('#licensesTable tbody').on('click', 'a.single-delete-btn, a[title="禁用"], a[title="启用"]', function(e) {
    var action = '';
    if ($(this).hasClass('single-delete-btn')) {
      action = '删除';
    } else if ($(this).attr('title') === '禁用') {
      action = '禁用';
    } else if ($(this).attr('title') === '启用') {
      action = '启用';
    }

    if (action && !confirm('确定要' + action + '这个激活码吗？')) {
      e.preventDefault();
      return false;
    }
  });

  // ===================================================================
  // 8. 生成激活码表单AJAX提交
  // ===================================================================
  $('#generateKeysForm').on('submit', function(e) {
    e.preventDefault(); // 阻止默认提交

    var formData = new FormData(this);
    formData.append('ajax', '1'); // 标记为AJAX请求

    var $submitBtn = $(this).find('button[type="submit"]');
    var originalText = $submitBtn.text();

    // 显示加载状态
    $submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 生成中...');

    $.ajax({
      url: 'actions/generate_keys.php',
      type: 'POST',
      data: formData,
      processData: false,
      contentType: false,
      dataType: 'json',
      success: function(response) {
        if (response.success) {
          // 填充弹窗数据
          $('#generatedCount').text(response.count);
          $('#generatedPlan').text(response.plan_name);

          // 生成激活码文本
          var keysText = response.keys.map(function(item) {
            return response.plan_name + '：' + item.key;
          }).join('\n');

          $('#generatedKeysText').val(keysText);

          // 显示成功弹窗
          $('#generateSuccessModal').modal('show');

          // 重置表单
          $('#generateKeysForm')[0].reset();
          $('#count').val('1'); // 重置数量为1
        } else {
          alert('生成失败：' + response.error);
        }
      },
      error: function() {
        alert('请求失败，请检查网络连接');
      },
      complete: function() {
        // 恢复按钮状态
        $submitBtn.prop('disabled', false).text(originalText);
      }
    });
  });

  // 复制生成的激活码
  $('#copyGeneratedKeys').on('click', function() {
    var $textarea = $('#generatedKeysText');
    $textarea.select();
    try {
      document.execCommand('copy');
      $(this).html('<i class="fas fa-check"></i> 已复制').removeClass('btn-primary').addClass('btn-success');
      setTimeout(() => {
        $(this).html('<i class="fas fa-copy"></i> 复制所有激活码').removeClass('btn-success').addClass('btn-primary');
      }, 2000);
    } catch (err) {
      alert('复制失败，请手动复制');
    }
  });

  // ===================================================================
  // 9. 表头筛选功能
  // ===================================================================

  // 点击筛选表头显示/隐藏筛选选项
  $('.filterable-header').on('click', function(e) {
    e.stopPropagation();
    var $this = $(this);
    var $dropdown = $this.find('.filter-dropdown');

    // 隐藏其他筛选下拉框
    $('.filter-dropdown').removeClass('show').hide();

    // 切换当前下拉框
    if ($dropdown.hasClass('show')) {
      $dropdown.removeClass('show').hide();
    } else {
      $dropdown.addClass('show').show();
    }
  });

  // 点击筛选选项
  $(document).on('click', '.filter-option', function(e) {
    e.preventDefault();
    var $this = $(this);
    var filterValue = $this.data('value');
    var $header = $this.closest('.filterable-header');
    var filterType = $header.data('filter');

    // 应用筛选
    applyFilter(filterType, filterValue);

    // 隐藏下拉框
    $header.find('.filter-dropdown').removeClass('show').hide();

    // 更新表头显示
    updateFilterHeader($header, filterValue);
  });

  // 套餐搜索功能
  $('#planFilter').on('input', function() {
    var searchText = $(this).val().toLowerCase();
    $(this).closest('.dropdown-menu').find('.filter-option').each(function() {
      var optionText = $(this).text().toLowerCase();
      $(this).toggle(optionText.includes(searchText));
    });
  });

  // 清除所有筛选
  $('#clearFiltersBtn').on('click', function() {
    table.search('').columns().search('').draw();
    $('.filterable-header').each(function() {
      updateFilterHeader($(this), '');
    });
  });

  // 点击其他地方隐藏筛选下拉框
  $(document).on('click', function() {
    $('.filter-dropdown').removeClass('show').hide();
  });

  // 应用筛选函数
  function applyFilter(filterType, filterValue) {
    if (filterType === 'plan') {
      // 按套餐筛选（第4列，索引为3）- 使用精确匹配
      if (filterValue === '') {
        table.column(3).search('').draw();
      } else {
        // 使用正则表达式进行精确匹配
        table.column(3).search('^' + filterValue.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') + '$', true, false).draw();
      }
    } else if (filterType === 'status') {
      // 按状态筛选（第5列，索引为4）- 使用精确匹配
      if (filterValue === '') {
        table.column(4).search('').draw();
      } else {
        // 使用正则表达式进行精确匹配
        table.column(4).search('^' + filterValue.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') + '$', true, false).draw();
      }
    }
  }

  // 更新筛选表头显示
  function updateFilterHeader($header, filterValue) {
    var $icon = $header.find('i');
    if (filterValue) {
      $icon.removeClass('text-muted').addClass('text-primary');
      $header.addClass('filtered');
    } else {
      $icon.removeClass('text-primary').addClass('text-muted');
      $header.removeClass('filtered');
    }
  }

  // ===================================================================
  // 10. 激活码点击复制功能
  // ===================================================================
  $(document).on('click', '.license-key-text', function() {
    var licenseKey = $(this).data('key');
    var $this = $(this);

    // 创建临时文本区域
    var tempTextArea = document.createElement('textarea');
    tempTextArea.value = licenseKey;
    document.body.appendChild(tempTextArea);
    tempTextArea.select();

    try {
      document.execCommand('copy');

      // 显示复制成功提示
      var originalText = $this.text();
      var originalColor = $this.css('color');

      $this.text('已复制!').css('color', '#28a745');

      setTimeout(function() {
        $this.text(originalText).css('color', originalColor);
      }, 1000);

    } catch (err) {
      alert('复制失败，请手动复制');
    }

    document.body.removeChild(tempTextArea);
  });

  // ===================================================================
  // 11. 增加设备数功能
  // ===================================================================
  $('#licensesTable tbody').on('click', '.add-devices-btn', function() {
    var $btn = $(this);
    var $row = $btn.closest('tr');
    var licenseId = $btn.data('id');
    var currentDevices = $btn.data('current');
    var licenseKey = $row.find('.license-key-text').data('key');

    // 填充模态框数据
    $('#addDevicesModal').find('#modalDevicesLicenseId').val(licenseId);
    $('#addDevicesModal').find('#modalDevicesLicenseKey').text(licenseKey);
    $('#addDevicesModal').find('#modalCurrentDevices').text(currentDevices + ' 台设备');
    $('#addDevicesModal').find('#new_max_devices').val(currentDevices);

    // 显示模态框
    $('#addDevicesModal').modal('show');
  });

});


// 【【【 在 main.js 文件末尾追加这段新代码 】】】

/*********************************************************************
 * 
 *                     套餐管理页面 (plans.php) 的交互逻辑
 * 
 *********************************************************************/
$(function() {
    // 检查当前页面是否是 plans.php，如果是才执行下面的代码
    // 这样可以避免在 index.php 页面上执行不必要的JS
    if ($('body').find('#planFormCard').length > 0) {

        // 点击“编辑”按钮，将该行数据填充到右侧的表单中
        $('.edit-plan-btn').on('click', function() {
            var button = $(this);
            
            // 改变表单标题和按钮文字，进入“编辑模式”
            $('#planFormTitle').html('<i class="fas fa-edit"></i> 编辑套餐');
            $('#planSubmitBtn').text('确认修改');
            $('#planAction').val('edit');

            // 从按钮的 data-* 属性中获取数据并填充到表单
            $('#planId').val(button.data('id'));
            $('#plan_name').val(button.data('name'));
            $('#validity_value').val(button.data('value'));
            $('#validity_unit').val(button.data('unit'));
            $('#max_devices').val(button.data('max-devices') || 1);
            $('#notes').val(button.data('notes'));
            
            // 设置“是否可用”的开关状态
            // data('active') 返回的是数字 1 或 0，需要与 1 比较
            $('#is_active').prop('checked', button.data('active') == 1);

            // 显示“取消编辑”按钮
            $('#cancelEditBtn').show();
            
            // 为了更好的用户体验，平滑滚动到表单位置
            $('html, body').animate({
                scrollTop: $("#planFormCard").offset().top - 60 // 减去60是为了留出导航栏的高度
            }, 500);
        });

        // 点击“取消编辑”按钮，重置表单回到“添加模式”
        $('#cancelEditBtn').on('click', function() {
            $('#planFormTitle').html('<i class="fas fa-plus"></i> 添加新套餐');
            $('#planSubmitBtn').text('确认添加');
            $('#planAction').val('add');
            
            // 使用 reset() 方法清空表单所有输入
            $('#planForm')[0].reset();
            
            // 确保“是否可用”开关恢复默认选中状态
            $('#is_active').prop('checked', true);
            
            // 隐藏自己
            $(this).hide();
        });
    }
});